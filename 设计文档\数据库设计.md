# SOC智能应答系统 - 数据库设计

## 1. 概述

本文档描述了SOC智能应答系统的数据库表结构设计，基于需求文档中的功能模块进行设计。

## 2. 数据库表结构

### 2.1 用户相关表

#### 2.1.1 用户表 (soc_users)
```sql
CREATE TABLE soc_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    user_code VARCHAR(50) NOT NULL UNIQUE COMMENT '用户工号',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    email VARCHAR(200) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    department VARCHAR(100) COMMENT '部门',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_code (user_code),
    INDEX idx_user_name (user_name)
) COMMENT='用户表';
```

#### 2.1.2 用户权限表 (soc_user_permissions)
```sql
CREATE TABLE soc_user_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    permission_type VARCHAR(50) NOT NULL COMMENT '权限类型：SOC_USER-普通用户',
    product_permissions TEXT COMMENT '产品权限JSON，存储用户可访问的产品列表',
    status TINYINT DEFAULT 1 COMMENT '状态：1-有效，0-无效',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES soc_users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_permission_type (permission_type)
) COMMENT='用户权限表';
```

### 2.2 任务相关表

#### 2.2.1 任务表 (soc_tasks)
```sql
CREATE TABLE soc_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码，系统自动生成',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country_mto VARCHAR(100) COMMENT '国家/MTO',
    mto_branch VARCHAR(100) COMMENT 'MTO分支',
    customer VARCHAR(200) COMMENT '客户',
    project VARCHAR(200) COMMENT '项目',
    data_source VARCHAR(50) DEFAULT 'GBBS' COMMENT '数据源：GBBS、文档库、项目文档、历史SOC文档',
    task_type TINYINT DEFAULT 1 COMMENT '任务类型：1-普通任务，2-个人任务',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (creator_id) REFERENCES soc_users(id),
    INDEX idx_task_code (task_code),
    INDEX idx_task_name (task_name),
    INDEX idx_creator_id (creator_id),
    INDEX idx_country_mto (country_mto),
    INDEX idx_customer (customer),
    INDEX idx_project (project)
) COMMENT='任务表';
```

### 2.3 条目相关表

#### 2.3.1 条目表 (soc_items)
```sql
CREATE TABLE soc_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    item_description TEXT NOT NULL COMMENT '条目描述',
    supplement_info TEXT COMMENT '补充信息',
    tags VARCHAR(500) COMMENT '标签，多个标签用逗号分隔',
    assigned_to BIGINT COMMENT '指派给用户ID',
    auto_answer TINYINT DEFAULT 1 COMMENT '自动应答：1-是，0-否',
    overwrite_on_duplicate TINYINT DEFAULT 1 COMMENT '重复时覆盖：1-是，0-否',
    remarks TEXT COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人ID',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (task_id) REFERENCES soc_tasks(id),
    FOREIGN KEY (assigned_to) REFERENCES soc_users(id),
    FOREIGN KEY (created_by) REFERENCES soc_users(id),
    FOREIGN KEY (updated_by) REFERENCES soc_users(id),
    UNIQUE KEY uk_task_item (task_id, item_code),
    INDEX idx_task_id (task_id),
    INDEX idx_item_code (item_code),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_tags (tags)
) COMMENT='条目表';
```

#### 2.3.2 条目应答表 (soc_item_responses)
```sql
CREATE TABLE soc_item_responses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '应答ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    product VARCHAR(200) NOT NULL COMMENT '产品',
    response_status TINYINT DEFAULT 0 COMMENT '应答状态：0-未应答，1-应答中，2-已应答',
    compliance_level VARCHAR(10) COMMENT '满足度：FC-完全满足，PC-部分满足，NC-不满足',
    response_content TEXT COMMENT '应答说明，支持富文本',
    response_method VARCHAR(20) COMMENT '应答方式：AI、手工',
    response_source VARCHAR(50) COMMENT '应答来源：GBBS、文档库等',
    source_index VARCHAR(500) COMMENT '索引信息',
    match_score DECIMAL(5,2) COMMENT '匹配度分数',
    version INT DEFAULT 1 COMMENT '版本号',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人ID',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (item_id) REFERENCES soc_items(id),
    FOREIGN KEY (created_by) REFERENCES soc_users(id),
    FOREIGN KEY (updated_by) REFERENCES soc_users(id),
    UNIQUE KEY uk_item_product (item_id, product),
    INDEX idx_item_id (item_id),
    INDEX idx_product (product),
    INDEX idx_response_status (response_status),
    INDEX idx_compliance_level (compliance_level),
    INDEX idx_response_method (response_method)
) COMMENT='条目应答表';
```

### 2.4 AI匹配相关表

#### 2.4.1 AI匹配结果表 (soc_ai_matches)
```sql
CREATE TABLE soc_ai_matches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    product VARCHAR(200) NOT NULL COMMENT '产品',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源',
    source_item_id VARCHAR(100) COMMENT '源条目ID',
    source_description TEXT COMMENT '源条目描述',
    match_score DECIMAL(5,2) NOT NULL COMMENT '匹配度分数',
    country_match TINYINT DEFAULT 0 COMMENT '国家匹配：1-匹配，0-不匹配',
    branch_match TINYINT DEFAULT 0 COMMENT '分支匹配：1-匹配，0-不匹配',
    customer_match TINYINT DEFAULT 0 COMMENT '客户匹配：1-匹配，0-不匹配',
    compliance_level VARCHAR(10) COMMENT '满足度：FC、PC、NC',
    response_content TEXT COMMENT '应答内容',
    source_index VARCHAR(500) COMMENT '源索引',
    is_selected TINYINT DEFAULT 0 COMMENT '是否被选中：1-是，0-否',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (item_id) REFERENCES soc_items(id),
    INDEX idx_item_product (item_id, product),
    INDEX idx_match_score (match_score),
    INDEX idx_data_source (data_source),
    INDEX idx_is_selected (is_selected)
) COMMENT='AI匹配结果表';
```

### 2.5 系统配置表

#### 2.5.1 数据源配置表 (soc_data_sources)
```sql
CREATE TABLE soc_data_sources (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据源ID',
    source_code VARCHAR(50) NOT NULL UNIQUE COMMENT '数据源编码',
    source_name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    source_type VARCHAR(50) NOT NULL COMMENT '数据源类型：GBBS、文档库、项目文档、历史SOC文档',
    config_json TEXT COMMENT '配置信息JSON',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_source_code (source_code),
    INDEX idx_source_type (source_type)
) COMMENT='数据源配置表';
```

#### 2.5.2 产品目录表 (soc_products)
```sql
CREATE TABLE soc_products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '产品ID',
    product_code VARCHAR(100) NOT NULL UNIQUE COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    parent_id BIGINT COMMENT '父级产品ID',
    product_level INT DEFAULT 1 COMMENT '产品层级',
    product_path VARCHAR(1000) COMMENT '产品路径',
    category VARCHAR(100) COMMENT '产品分类：SOC标准库、SOC积累库、产品目录树',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_id) REFERENCES soc_products(id),
    INDEX idx_product_code (product_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_category (category)
) COMMENT='产品目录表';
```

### 2.6 操作日志表

#### 2.6.1 操作日志表 (soc_operation_logs)
```sql
CREATE TABLE soc_operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_module VARCHAR(50) NOT NULL COMMENT '操作模块',
    target_id BIGINT COMMENT '目标对象ID',
    operation_content TEXT COMMENT '操作内容',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    FOREIGN KEY (user_id) REFERENCES soc_users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_module (operation_module),
    INDEX idx_operation_time (operation_time)
) COMMENT='操作日志表';
```

## 3. 索引设计说明

1. **主键索引**：所有表都使用自增主键
2. **唯一索引**：确保业务唯一性约束
3. **外键索引**：提高关联查询性能
4. **业务索引**：根据查询场景设计复合索引

## 4. 数据字典

### 4.1 应答状态枚举
- 0: 未应答
- 1: 应答中  
- 2: 已应答

### 4.2 满足度枚举
- FC: 完全满足 (Full Compliance)
- PC: 部分满足 (Partially Compliance)  
- NC: 不满足 (Not Compliance)

### 4.3 应答方式枚举
- AI: AI自动应答
- 手工: 人工应答

### 4.4 数据源类型枚举
- GBBS: GBBS系统
- 文档库: 文档库系统
- 项目文档: 项目文档
- 历史SOC文档: 历史SOC文档

### 2.7 文件管理表

#### 2.7.1 文件上传表 (soc_files)
```sql
CREATE TABLE soc_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    upload_user_id BIGINT NOT NULL COMMENT '上传用户ID',
    related_type VARCHAR(50) COMMENT '关联类型：TASK_IMPORT-任务导入',
    related_id BIGINT COMMENT '关联对象ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (upload_user_id) REFERENCES soc_users(id),
    INDEX idx_upload_user (upload_user_id),
    INDEX idx_related (related_type, related_id),
    INDEX idx_file_name (file_name)
) COMMENT='文件上传表';
```

### 2.8 标签管理表

#### 2.8.1 标签表 (soc_tags)
```sql
CREATE TABLE soc_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    tag_color VARCHAR(20) COMMENT '标签颜色',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (created_by) REFERENCES soc_users(id),
    UNIQUE KEY uk_tag_name (tag_name),
    INDEX idx_usage_count (usage_count)
) COMMENT='标签表';
```

#### 2.8.2 条目标签关联表 (soc_item_tags)
```sql
CREATE TABLE soc_item_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (item_id) REFERENCES soc_items(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES soc_tags(id) ON DELETE CASCADE,
    UNIQUE KEY uk_item_tag (item_id, tag_id),
    INDEX idx_item_id (item_id),
    INDEX idx_tag_id (tag_id)
) COMMENT='条目标签关联表';
```

### 2.9 相似度分析表

#### 2.9.1 条目相似度表 (soc_item_similarity)
```sql
CREATE TABLE soc_item_similarity (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '相似度ID',
    source_item_id BIGINT NOT NULL COMMENT '源条目ID',
    target_item_id BIGINT NOT NULL COMMENT '目标条目ID',
    similarity_score DECIMAL(5,4) NOT NULL COMMENT '相似度分数(0-1)',
    similarity_type VARCHAR(50) DEFAULT 'CONTENT' COMMENT '相似度类型：CONTENT-内容相似，TAG-标签相似',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (source_item_id) REFERENCES soc_items(id),
    FOREIGN KEY (target_item_id) REFERENCES soc_items(id),
    INDEX idx_source_item (source_item_id),
    INDEX idx_target_item (target_item_id),
    INDEX idx_similarity_score (similarity_score),
    INDEX idx_similarity_type (similarity_type)
) COMMENT='条目相似度表';
```

## 5. 表关系说明

1. **用户-任务关系**：一个用户可以创建多个任务
2. **任务-条目关系**：一个任务包含多个条目
3. **条目-应答关系**：一个条目可以有多个产品维度的应答
4. **条目-匹配关系**：一个条目可以有多个AI匹配结果
5. **用户权限关系**：用户通过权限表控制产品访问权限
6. **条目-标签关系**：多对多关系，通过关联表实现
7. **条目相似度关系**：条目之间的相似度分析结果

## 6. 数据库初始化脚本

### 6.1 初始数据源配置
```sql
INSERT INTO soc_data_sources (source_code, source_name, source_type, status) VALUES
('GBBS', 'GBBS系统', 'GBBS', 1),
('DOC_LIB', '文档库', '文档库', 1),
('PROJECT_DOC', '项目文档', '项目文档', 1),
('HISTORY_SOC', '历史SOC文档', '历史SOC文档', 1);
```

### 6.2 初始产品目录
```sql
INSERT INTO soc_products (product_code, product_name, parent_id, product_level, category, sort_order) VALUES
('SOC_STD', 'SOC标准库', NULL, 1, 'SOC标准库', 1),
('SOC_ACC', 'SOC积累库', NULL, 1, 'SOC积累库', 2),
('PRODUCT_TREE', '产品目录树', NULL, 1, '产品目录树', 3);
```

## 7. 性能优化建议

1. **分区策略**：对于大表如操作日志表，可按时间分区
2. **索引优化**：根据实际查询场景调整索引
3. **读写分离**：考虑主从复制，读写分离
4. **缓存策略**：热点数据使用Redis缓存
5. **归档策略**：历史数据定期归档

## 8. 数据安全考虑

1. **敏感数据加密**：用户个人信息加密存储
2. **访问控制**：基于角色的数据访问控制
3. **审计日志**：完整的操作审计日志
4. **备份策略**：定期数据备份和恢复测试
