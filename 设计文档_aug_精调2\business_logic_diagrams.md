# SOC智能应答系统 - 业务逻辑图表

## 目录
1. [系统架构图](#系统架构图)
2. [业务流程图](#业务流程图)
3. [数据库ER图](#数据库er图)
4. [API调用时序图](#api调用时序图)
5. [用户角色权限图](#用户角色权限图)

## 系统架构图

### 整体系统架构
以下图表展示了SOC智能应答系统的整体架构，包括前端应用、后端服务、数据存储和外部服务之间的关系。

```mermaid
graph TB
    subgraph "前端层"
        WebUI[Web前端界面]
        MobileUI[移动端界面]
    end
    
    subgraph "网关层"
        Gateway[API网关]
        Auth[认证服务]
        RateLimit[限流服务]
    end
    
    subgraph "应用服务层"
        UserService[用户管理服务]
        TaskService[任务管理服务]
        ItemService[条目管理服务]
        ResponseService[应答管理服务]
        AnalysisService[数据分析服务]
        FileService[文件管理服务]
    end
    
    subgraph "数据存储层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        FileStorage[(文件存储)]
    end
    
    subgraph "外部服务"
        GBBS[GBBS系统]
        AIService[AI智能服务]
        EmailService[邮件服务]
        ProjectAPI[项目管理API]
    end
    
    subgraph "基础设施"
        Monitor[监控告警]
        Log[日志系统]
        Config[配置中心]
    end
    
    %% 连接关系
    WebUI --> Gateway
    MobileUI --> Gateway
    
    Gateway --> Auth
    Gateway --> RateLimit
    Gateway --> UserService
    Gateway --> TaskService
    Gateway --> ItemService
    Gateway --> ResponseService
    Gateway --> AnalysisService
    Gateway --> FileService
    
    UserService --> MySQL
    TaskService --> MySQL
    ItemService --> MySQL
    ResponseService --> MySQL
    AnalysisService --> MySQL
    FileService --> FileStorage
    
    UserService --> Redis
    TaskService --> Redis
    ItemService --> Redis
    ResponseService --> Redis
    
    ResponseService --> GBBS
    ResponseService --> AIService
    TaskService --> ProjectAPI
    UserService --> EmailService
    
    UserService --> Monitor
    TaskService --> Monitor
    ItemService --> Monitor
    ResponseService --> Monitor
    
    UserService --> Log
    TaskService --> Log
    ItemService --> Log
    ResponseService --> Log
    
    UserService --> Config
    TaskService --> Config
    ItemService --> Config
    ResponseService --> Config
```

**架构说明：**
- **前端层**：提供Web和移动端用户界面
- **网关层**：统一入口，处理认证、限流等横切关注点
- **应用服务层**：核心业务逻辑，按功能模块划分微服务
- **数据存储层**：MySQL存储业务数据，Redis提供缓存，文件存储管理附件
- **外部服务**：集成GBBS、AI服务等外部系统
- **基础设施**：提供监控、日志、配置等基础能力

## 业务流程图

### 1. 任务创建和管理流程

```mermaid
flowchart TD
    Start([用户登录系统]) --> CheckAuth{验证用户权限}
    CheckAuth -->|权限不足| AuthError[显示权限错误]
    CheckAuth -->|权限通过| TaskMgmt[进入任务管理页面]
    
    TaskMgmt --> CreateTask[点击创建任务]
    CreateTask --> FillForm[填写任务信息]
    FillForm --> ValidateForm{表单验证}
    ValidateForm -->|验证失败| FormError[显示验证错误]
    ValidateForm -->|验证通过| SaveTask[保存任务]
    
    SaveTask --> CheckDuplicate{检查任务名重复}
    CheckDuplicate -->|重复| DuplicateError[显示重复错误]
    CheckDuplicate -->|不重复| CreateSuccess[任务创建成功]
    
    CreateSuccess --> UploadFile{是否上传条目文件}
    UploadFile -->|是| ProcessFile[处理Excel文件]
    UploadFile -->|否| TaskDetail[进入任务详情]
    
    ProcessFile --> ParseExcel{解析Excel}
    ParseExcel -->|解析失败| FileError[显示文件错误]
    ParseExcel -->|解析成功| ImportItems[批量导入条目]
    
    ImportItems --> AutoResponse{开启自动应答}
    AutoResponse -->|是| StartAI[启动AI应答]
    AutoResponse -->|否| TaskDetail
    
    StartAI --> TaskDetail
    TaskDetail --> End([流程结束])
    
    FormError --> FillForm
    DuplicateError --> FillForm
    FileError --> UploadFile
    AuthError --> End
```

### 2. 条目录入和应答流程

```mermaid
flowchart TD
    Start([进入条目管理]) --> ViewItems[查看条目列表]
    ViewItems --> AddItem{添加条目方式}
    
    AddItem -->|单条录入| SingleAdd[单条录入表单]
    AddItem -->|批量导入| BatchImport[批量导入文件]
    AddItem -->|查看现有| ItemList[条目列表操作]
    
    SingleAdd --> FillItemForm[填写条目信息]
    FillItemForm --> ValidateItem{验证条目信息}
    ValidateItem -->|验证失败| ItemError[显示错误信息]
    ValidateItem -->|验证通过| SaveItem[保存条目]
    
    BatchImport --> UploadExcel[上传Excel文件]
    UploadExcel --> ParseFile{解析文件}
    ParseFile -->|解析失败| FileError[文件格式错误]
    ParseFile -->|解析成功| ValidateBatch{批量验证数据}
    ValidateBatch -->|有错误| ShowErrors[显示错误详情]
    ValidateBatch -->|验证通过| BatchSave[批量保存条目]
    
    SaveItem --> CheckAutoResponse{是否自动应答}
    BatchSave --> CheckAutoResponse
    
    CheckAutoResponse -->|是| TriggerAI[触发AI应答]
    CheckAutoResponse -->|否| ItemSaved[条目保存完成]
    
    TriggerAI --> AIProcessing[AI处理中]
    AIProcessing --> AIComplete[AI应答完成]
    AIComplete --> ItemSaved
    
    ItemList --> SelectItems[选择条目操作]
    SelectItems --> BatchOp{批量操作类型}
    
    BatchOp -->|开始应答| BatchResponse[批量启动应答]
    BatchOp -->|添加标签| AddTags[批量添加标签]
    BatchOp -->|设置产品| SetProduct[设置产品]
    BatchOp -->|指派给| AssignTo[指派给用户]
    BatchOp -->|删除| DeleteItems[批量删除]
    
    BatchResponse --> TriggerAI
    AddTags --> ItemSaved
    SetProduct --> ItemSaved
    AssignTo --> ItemSaved
    DeleteItems --> ItemSaved
    
    ItemSaved --> End([流程结束])
    ItemError --> FillItemForm
    FileError --> UploadExcel
    ShowErrors --> BatchImport
```

### 3. AI智能应答流程

```mermaid
flowchart TD
    Start([AI应答触发]) --> GetContext[获取应答上下文]
    GetContext --> ExtractInfo[提取关键信息]
    
    ExtractInfo --> TaskInfo{任务信息}
    TaskInfo --> Country[国家/MTO]
    TaskInfo --> Branch[MTO分支]
    TaskInfo --> Customer[客户信息]
    TaskInfo --> Product[产品信息]
    
    ExtractInfo --> ItemInfo{条目信息}
    ItemInfo --> ItemDesc[条目描述]
    ItemInfo --> Supplement[补充信息]
    ItemInfo --> Tags[标签信息]
    
    Country --> BuildQuery[构建查询条件]
    Branch --> BuildQuery
    Customer --> BuildQuery
    Product --> BuildQuery
    ItemDesc --> BuildQuery
    Supplement --> BuildQuery
    Tags --> BuildQuery
    
    BuildQuery --> QueryGBBS[查询GBBS数据源]
    QueryGBBS --> GBBSResult{GBBS查询结果}
    
    GBBSResult -->|无结果| NoMatch[无匹配结果]
    GBBSResult -->|有结果| CalcMatch[计算匹配度]
    
    CalcMatch --> ScoreMatch[评分匹配项]
    ScoreMatch --> RankResults[结果排序]
    RankResults --> SelectBest[选择最佳匹配]
    
    SelectBest --> ExtractAnswer[提取应答内容]
    ExtractAnswer --> ProcessContent[处理应答内容]
    ProcessContent --> GenerateResponse[生成应答结果]
    
    GenerateResponse --> SaveResponse[保存应答记录]
    SaveResponse --> SaveMatches[保存匹配记录]
    SaveMatches --> UpdateStatus[更新条目状态]
    
    NoMatch --> DefaultResponse[生成默认应答]
    DefaultResponse --> SaveResponse
    
    UpdateStatus --> NotifyUser[通知用户]
    NotifyUser --> End([AI应答完成])

### 4. 人工应答和审核流程

```mermaid
flowchart TD
    Start([进入人工应答]) --> SelectItem[选择条目]
    SelectItem --> LoadDetail[加载条目详情]
    LoadDetail --> ViewTabs{查看页签}

    ViewTabs -->|应答结果| ResponseTab[应答结果页签]
    ViewTabs -->|匹配详情| MatchTab[匹配详情页签]

    ResponseTab --> EditResponse[编辑应答内容]
    EditResponse --> ModifyFields{修改字段}

    ModifyFields -->|补充信息| AddSupplement[添加补充信息]
    ModifyFields -->|满足度| ChangeSatisfaction[修改满足度]
    ModifyFields -->|应答说明| EditContent[编辑应答说明]
    ModifyFields -->|索引| UpdateIndex[更新索引]
    ModifyFields -->|备注| AddRemark[添加备注]

    EditContent --> ContentOp{内容操作}
    ContentOp -->|AI润色| AIPolish[AI润色内容]
    ContentOp -->|AI翻译| AITranslate[AI翻译内容]
    ContentOp -->|插入图片| InsertImage[插入图片]
    ContentOp -->|手工编辑| ManualEdit[手工编辑]

    AIPolish --> ReviewAI{审核AI结果}
    AITranslate --> ReviewAI
    ReviewAI -->|接受| ApplyAI[应用AI结果]
    ReviewAI -->|拒绝| ManualEdit

    ApplyAI --> SaveChanges[保存修改]
    ManualEdit --> SaveChanges
    InsertImage --> SaveChanges
    AddSupplement --> SaveChanges
    ChangeSatisfaction --> SaveChanges
    UpdateIndex --> SaveChanges
    AddRemark --> SaveChanges

    MatchTab --> ViewMatches[查看匹配列表]
    ViewMatches --> FilterMatches{筛选匹配}
    FilterMatches -->|按满足度| FilterSatisfaction[按满足度筛选]
    FilterMatches -->|按匹配度| FilterScore[按匹配度筛选]
    FilterMatches -->|按数据源| FilterSource[按数据源筛选]

    FilterSatisfaction --> ShowFiltered[显示筛选结果]
    FilterScore --> ShowFiltered
    FilterSource --> ShowFiltered

    ShowFiltered --> SelectMatch[选择匹配项]
    SelectMatch --> ViewMatchDetail[查看匹配详情]
    ViewMatchDetail --> ApplyMatch{应用匹配}

    ApplyMatch -->|确认应用| ConfirmApply[确认应用匹配]
    ApplyMatch -->|取消| ViewMatches

    ConfirmApply --> OverwriteWarning[覆盖警告]
    OverwriteWarning --> ConfirmOverwrite{确认覆盖}
    ConfirmOverwrite -->|确认| ApplyMatchResult[应用匹配结果]
    ConfirmOverwrite -->|取消| ViewMatchDetail

    ApplyMatchResult --> UpdateResponse[更新应答内容]
    UpdateResponse --> SaveChanges

    SaveChanges --> UpdateMethod[更新应答方式]
    UpdateMethod --> LogOperation[记录操作日志]
    LogOperation --> RefreshView[刷新界面]
    RefreshView --> End([人工应答完成])

## 数据库ER图

### 核心实体关系图
以下ER图展示了SOC智能应答系统8个核心数据表之间的关联关系。

```mermaid
erDiagram
    soc_user {
        bigint id PK
        varchar user_code UK
        varchar user_name
        varchar email
        varchar department
        varchar role
        tinyint status
        datetime created_time
        datetime updated_time
        tinyint is_deleted
    }

    soc_task {
        bigint id PK
        varchar task_code UK
        varchar task_name
        varchar country
        varchar mto_branch
        varchar customer
        varchar project
        varchar data_source
        bigint creator_id FK
        varchar creator_name
        int total_items
        int answered_items
        int fc_count
        int pc_count
        int nc_count
        decimal satisfaction_rate
        varchar status
        datetime created_time
        datetime updated_time
        tinyint is_deleted
    }

    soc_item {
        bigint id PK
        bigint task_id FK
        varchar item_code
        text item_description
        text supplement_info
        bigint assignee_id FK
        varchar assignee_name
        varchar status
        tinyint auto_response
        tinyint overwrite_on_duplicate
        text remark
        bigint creator_id FK
        varchar creator_name
        datetime created_time
        datetime updated_time
        bigint last_updated_by FK
        varchar last_updated_name
        tinyint is_deleted
    }

    soc_response {
        bigint id PK
        bigint item_id FK
        varchar product
        varchar satisfaction
        longtext response_content
        varchar response_method
        varchar data_source
        varchar source_index
        decimal match_score
        int version
        varchar status
        bigint creator_id FK
        varchar creator_name
        datetime created_time
        datetime updated_time
        bigint last_updated_by FK
        varchar last_updated_name
        tinyint is_deleted
    }

    soc_tag {
        bigint id PK
        varchar tag_name UK
        varchar tag_color
        int usage_count
        bigint creator_id FK
        datetime created_time
        tinyint is_deleted
    }

    soc_item_tag {
        bigint id PK
        bigint item_id FK
        bigint tag_id FK
        datetime created_time
    }

    soc_match_record {
        bigint id PK
        bigint response_id FK
        varchar data_source
        varchar source_item_code
        text source_description
        varchar source_satisfaction
        longtext source_content
        varchar source_index
        decimal match_score
        tinyint country_match
        tinyint branch_match
        tinyint customer_match
        tinyint is_applied
        datetime created_time
    }

    soc_operation_log {
        bigint id PK
        bigint user_id FK
        varchar user_name
        varchar operation_type
        varchar operation_desc
        varchar target_type
        bigint target_id
        varchar ip_address
        varchar user_agent
        datetime operation_time
    }

    %% 关系定义
    soc_user ||--o{ soc_task : "创建"
    soc_user ||--o{ soc_item : "创建"
    soc_user ||--o{ soc_item : "指派"
    soc_user ||--o{ soc_response : "创建"
    soc_user ||--o{ soc_tag : "创建"
    soc_user ||--o{ soc_operation_log : "操作"

    soc_task ||--o{ soc_item : "包含"
    soc_item ||--o{ soc_response : "应答"
    soc_item ||--o{ soc_item_tag : "标记"
    soc_tag ||--o{ soc_item_tag : "标记"
    soc_response ||--o{ soc_match_record : "匹配"
```

**ER图说明：**
- **实体关系**：展示了8个核心表的字段结构和主要约束
- **关联关系**：用线条表示表之间的外键关联关系
- **基数关系**：||--o{ 表示一对多关系，||--|| 表示一对一关系
- **核心流程**：用户创建任务→添加条目→生成应答→记录匹配→操作日志

## API调用时序图

### 典型业务流程API调用序列
以下时序图展示了"创建任务→添加条目→AI应答→人工审核"的完整API调用流程。

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端应用
    participant Gateway as API网关
    participant AuthService as 认证服务
    participant TaskService as 任务服务
    participant ItemService as 条目服务
    participant ResponseService as 应答服务
    participant AIService as AI服务
    participant GBBS as GBBS系统
    participant Database as 数据库

    %% 用户登录
    User->>Frontend: 输入登录信息
    Frontend->>Gateway: POST /api/v1/auth/login
    Gateway->>AuthService: 验证用户凭证
    AuthService->>Database: 查询用户信息
    Database-->>AuthService: 返回用户数据
    AuthService-->>Gateway: 返回JWT Token
    Gateway-->>Frontend: 登录成功响应
    Frontend-->>User: 显示主界面

    %% 创建任务
    User->>Frontend: 点击创建任务
    Frontend->>Gateway: POST /api/v1/tasks
    Gateway->>AuthService: 验证Token
    AuthService-->>Gateway: Token有效
    Gateway->>TaskService: 创建任务请求
    TaskService->>Database: 保存任务信息
    Database-->>TaskService: 任务创建成功
    TaskService-->>Gateway: 返回任务详情
    Gateway-->>Frontend: 任务创建响应
    Frontend-->>User: 显示任务详情页

    %% 添加条目
    User->>Frontend: 添加条目信息
    Frontend->>Gateway: POST /api/v1/tasks/{taskId}/items
    Gateway->>ItemService: 创建条目请求
    ItemService->>Database: 保存条目信息
    Database-->>ItemService: 条目创建成功

    %% 自动触发AI应答
    ItemService->>ResponseService: 触发AI应答
    ResponseService->>AIService: 请求AI分析
    AIService->>GBBS: 查询匹配数据
    GBBS-->>AIService: 返回匹配结果
    AIService->>AIService: 计算匹配度
    AIService-->>ResponseService: 返回AI应答结果
    ResponseService->>Database: 保存应答和匹配记录
    Database-->>ResponseService: 保存成功
    ResponseService-->>ItemService: AI应答完成
    ItemService-->>Gateway: 条目创建完成
    Gateway-->>Frontend: 条目创建响应
    Frontend-->>User: 显示条目列表

    %% 人工审核应答
    User->>Frontend: 点击人工应答
    Frontend->>Gateway: GET /api/v1/items/{itemId}
    Gateway->>ItemService: 获取条目详情
    ItemService->>Database: 查询条目和应答
    Database-->>ItemService: 返回详细信息
    ItemService-->>Gateway: 条目详情响应
    Gateway-->>Frontend: 返回条目数据
    Frontend-->>User: 显示应答详情页

    %% 修改应答内容
    User->>Frontend: 修改应答内容
    Frontend->>Gateway: PUT /api/v1/responses/{responseId}
    Gateway->>ResponseService: 更新应答请求
    ResponseService->>Database: 更新应答记录
    Database-->>ResponseService: 更新成功
    ResponseService->>TaskService: 更新任务统计
    TaskService->>Database: 更新任务数据
    Database-->>TaskService: 统计更新完成
    TaskService-->>ResponseService: 统计更新确认
    ResponseService-->>Gateway: 应答更新完成
    Gateway-->>Frontend: 更新成功响应
    Frontend-->>User: 显示更新结果

### 批量操作API调用序列

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端应用
    participant Gateway as API网关
    participant ItemService as 条目服务
    participant ResponseService as 应答服务
    participant AIService as AI服务
    participant Database as 数据库
    participant Queue as 消息队列

    %% 批量导入条目
    User->>Frontend: 上传Excel文件
    Frontend->>Gateway: POST /api/v1/files/upload
    Gateway->>ItemService: 文件上传请求
    ItemService->>ItemService: 解析Excel文件
    ItemService->>Database: 批量保存条目
    Database-->>ItemService: 保存结果
    ItemService-->>Gateway: 导入结果响应
    Gateway-->>Frontend: 返回导入统计
    Frontend-->>User: 显示导入结果

    %% 批量启动AI应答
    User->>Frontend: 选择条目批量应答
    Frontend->>Gateway: POST /api/v1/tasks/{taskId}/items/batch-operation
    Gateway->>ItemService: 批量操作请求

    loop 处理每个条目
        ItemService->>Queue: 发送AI应答任务
        Queue->>ResponseService: 异步处理应答
        ResponseService->>AIService: 请求AI分析
        AIService-->>ResponseService: 返回应答结果
        ResponseService->>Database: 保存应答记录
        Database-->>ResponseService: 保存确认
        ResponseService->>Queue: 任务完成通知
    end

    ItemService-->>Gateway: 批量操作已启动
    Gateway-->>Frontend: 操作启动响应
    Frontend-->>User: 显示处理进度

    %% 异步通知处理结果
    Queue->>Frontend: WebSocket推送进度
    Frontend-->>User: 实时更新进度

## 用户角色权限图

### 用户角色权限矩阵
以下图表展示了不同用户角色在SOC智能应答系统中的权限范围和操作权限。

```mermaid
graph TB
    subgraph "用户角色"
        Admin[系统管理员]
        TaskCreator[任务创建人]
        Assignee[条目指派人]
        NormalUser[普通用户]
    end

    subgraph "任务管理权限"
        CreateTask[创建任务]
        ViewAllTasks[查看所有任务]
        ViewOwnTasks[查看自己的任务]
        EditTask[编辑任务]
        DeleteTask[删除任务]
        CopyTask[复制任务]
    end

    subgraph "条目管理权限"
        AddItem[添加条目]
        BatchImport[批量导入]
        ViewAllItems[查看所有条目]
        ViewAssignedItems[查看指派条目]
        EditItem[编辑条目]
        DeleteItem[删除条目]
        AssignItem[指派条目]
    end

    subgraph "应答管理权限"
        ManualResponse[手工应答]
        AIResponse[AI应答]
        EditResponse[编辑应答]
        ViewResponse[查看应答]
        ApplyMatch[应用匹配]
        ExportResponse[导出应答]
    end

    subgraph "标签管理权限"
        CreateTag[创建标签]
        AddTag[添加标签]
        RemoveTag[移除标签]
        ManageTag[管理标签]
    end

    subgraph "数据分析权限"
        ViewAllStats[查看全部统计]
        ViewOwnStats[查看个人统计]
        ViewTaskStats[查看任务统计]
        ExportStats[导出统计]
    end

    %% 系统管理员权限
    Admin --> CreateTask
    Admin --> ViewAllTasks
    Admin --> EditTask
    Admin --> DeleteTask
    Admin --> CopyTask
    Admin --> AddItem
    Admin --> BatchImport
    Admin --> ViewAllItems
    Admin --> EditItem
    Admin --> DeleteItem
    Admin --> AssignItem
    Admin --> ManualResponse
    Admin --> AIResponse
    Admin --> EditResponse
    Admin --> ViewResponse
    Admin --> ApplyMatch
    Admin --> ExportResponse
    Admin --> CreateTag
    Admin --> AddTag
    Admin --> RemoveTag
    Admin --> ManageTag
    Admin --> ViewAllStats
    Admin --> ViewTaskStats
    Admin --> ExportStats

    %% 任务创建人权限
    TaskCreator --> CreateTask
    TaskCreator --> ViewOwnTasks
    TaskCreator --> EditTask
    TaskCreator --> DeleteTask
    TaskCreator --> CopyTask
    TaskCreator --> AddItem
    TaskCreator --> BatchImport
    TaskCreator --> ViewAllItems
    TaskCreator --> EditItem
    TaskCreator --> DeleteItem
    TaskCreator --> AssignItem
    TaskCreator --> ManualResponse
    TaskCreator --> AIResponse
    TaskCreator --> EditResponse
    TaskCreator --> ViewResponse
    TaskCreator --> ApplyMatch
    TaskCreator --> ExportResponse
    TaskCreator --> AddTag
    TaskCreator --> RemoveTag
    TaskCreator --> ViewTaskStats
    TaskCreator --> ExportStats

    %% 条目指派人权限
    Assignee --> ViewOwnTasks
    Assignee --> ViewAssignedItems
    Assignee --> ManualResponse
    Assignee --> AIResponse
    Assignee --> EditResponse
    Assignee --> ViewResponse
    Assignee --> ApplyMatch
    Assignee --> ExportResponse
    Assignee --> AddTag
    Assignee --> RemoveTag
    Assignee --> ViewOwnStats

    %% 普通用户权限
    NormalUser --> ViewOwnTasks
    NormalUser --> ViewResponse
    NormalUser --> ViewOwnStats
```

### 权限控制详细说明

```mermaid
mindmap
  root((权限控制体系))
    角色定义
      系统管理员
        全系统权限
        用户管理
        系统配置
      任务创建人
        任务完全控制
        条目管理
        应答管理
      条目指派人
        指派条目操作
        应答编辑
        数据查看
      普通用户
        只读权限
        个人数据
    权限验证
      接口级验证
        JWT Token
        角色检查
        操作权限
      数据级验证
        创建人验证
        指派人验证
        任务关联验证
      功能级验证
        菜单权限
        按钮权限
        页面权限
    权限继承
      任务权限
        创建人→全部条目
        指派人→指定条目
        普通用户→只读
      条目权限
        任务创建人→全部操作
        条目指派人→应答操作
        其他用户→无权限
      应答权限
        创建人→编辑删除
        指派人→编辑
        其他用户→只读
```

### 权限矩阵表

| 功能模块 | 系统管理员 | 任务创建人 | 条目指派人 | 普通用户 |
|----------|------------|------------|------------|----------|
| **任务管理** |
| 创建任务 | ✅ | ✅ | ❌ | ❌ |
| 查看所有任务 | ✅ | ❌ | ❌ | ❌ |
| 查看自己任务 | ✅ | ✅ | ✅ | ✅ |
| 编辑任务 | ✅ | ✅(自己的) | ❌ | ❌ |
| 删除任务 | ✅ | ✅(自己的) | ❌ | ❌ |
| 复制任务 | ✅ | ✅ | ❌ | ❌ |
| **条目管理** |
| 单条录入 | ✅ | ✅(自己任务) | ❌ | ❌ |
| 批量导入 | ✅ | ✅(自己任务) | ❌ | ❌ |
| 查看所有条目 | ✅ | ✅(自己任务) | ❌ | ❌ |
| 查看指派条目 | ✅ | ✅ | ✅ | ❌ |
| 编辑条目 | ✅ | ✅(自己任务) | ❌ | ❌ |
| 删除条目 | ✅ | ✅(自己任务) | ✅(指派的) | ❌ |
| 指派条目 | ✅ | ✅(自己任务) | ✅(指派的) | ❌ |
| **应答管理** |
| 手工应答 | ✅ | ✅ | ✅(指派的) | ❌ |
| AI应答 | ✅ | ✅ | ✅(指派的) | ❌ |
| 编辑应答 | ✅ | ✅ | ✅(指派的) | ❌ |
| 查看应答 | ✅ | ✅ | ✅(指派的) | ✅(只读) |
| 应用匹配 | ✅ | ✅ | ✅(指派的) | ❌ |
| 导出应答 | ✅ | ✅ | ✅(指派的) | ❌ |
| **标签管理** |
| 创建标签 | ✅ | ❌ | ❌ | ❌ |
| 添加标签 | ✅ | ✅ | ✅(指派的) | ❌ |
| 移除标签 | ✅ | ✅ | ✅(指派的) | ❌ |
| 管理标签 | ✅ | ❌ | ❌ | ❌ |
| **数据分析** |
| 查看全部统计 | ✅ | ❌ | ❌ | ❌ |
| 查看任务统计 | ✅ | ✅(自己任务) | ❌ | ❌ |
| 查看个人统计 | ✅ | ✅ | ✅ | ✅ |
| 导出统计 | ✅ | ✅(自己任务) | ✅(指派的) | ❌ |

## 图表总结

### 图表说明
本文档通过5类业务逻辑图表，全面展示了SOC智能应答系统的架构设计和业务流程：

1. **系统架构图**：展示了系统的分层架构，包括前端、网关、服务、存储和外部系统的关系
2. **业务流程图**：详细描述了任务管理、条目应答、AI处理、人工审核等核心业务流程
3. **数据库ER图**：清晰展示了8个核心数据表的结构和关联关系
4. **API调用时序图**：展示了典型业务场景下的API调用序列和交互过程
5. **用户角色权限图**：定义了不同用户角色的权限范围和访问控制策略

### 设计特点
- **模块化架构**：采用微服务架构，各模块职责清晰，便于维护和扩展
- **异步处理**：AI应答和批量操作采用异步处理，提高系统响应性能
- **权限控制**：细粒度的权限控制，确保数据安全和操作合规
- **数据完整性**：通过外键约束和业务逻辑保证数据的完整性和一致性
- **用户体验**：流程设计考虑了用户操作习惯，提供友好的交互体验

### 技术实现要点
- **前后端分离**：前端负责展示和交互，后端提供API服务
- **服务治理**：通过API网关统一管理服务访问和安全控制
- **数据存储**：MySQL存储业务数据，Redis提供缓存，文件系统管理附件
- **外部集成**：与GBBS、AI服务等外部系统集成，提供智能化能力
- **监控运维**：完善的日志记录和监控告警机制，保障系统稳定运行

这些图表为SOC智能应答系统的开发、测试和运维提供了清晰的指导，确保系统能够满足业务需求并具备良好的可维护性和扩展性。
