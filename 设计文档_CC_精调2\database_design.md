# SOC智能应答系统 - 数据库设计文档

## 目录
- [1. 概述](#1-概述)
- [2. 数据库设计原则](#2-数据库设计原则)
- [3. 业务实体分析](#3-业务实体分析)
- [4. 表结构设计](#4-表结构设计)
- [5. 索引设计](#5-索引设计)
- [6. 数据关系图](#6-数据关系图)
- [7. SQL建表语句](#7-sql建表语句)
- [8. 数据安全和扩展性](#8-数据安全和扩展性)

## 1. 概述

### 1.1 系统简介
SOC智能应答系统是一个基于AI技术的标书应答自动化平台，支持多数据源智能匹配、自动应答生成、人工应答优化等核心功能。

### 1.2 技术环境
- **数据库**: MySQL 8.0+
- **存储引擎**: InnoDB
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci

### 1.3 核心术语
- **条目(Item)**: 从标书中梳理出来的要求/问题
- **应答(Response)**: 对条目的回答，通常以产品维度对条目进行应答  
- **SOC**: Statement of Compliance，符合性声明
- **FC**: Full Compliance 完全满足
- **PC**: Partially Compliance 部分满足
- **NC**: Not Compliance 不满足

## 2. 数据库设计原则

### 2.1 设计原则
- **数据完整性**: 通过外键约束保证数据一致性
- **性能优化**: 合理设计索引，支持高频查询场景
- **扩展性**: 预留扩展字段，支持业务发展
- **安全性**: 敏感数据处理和完整审计日志
- **标准化**: 遵循数据库设计规范，避免数据冗余

### 2.2 命名规范
- **表名**: 使用下划线分隔的小写字母，前缀为 `soc_`
- **字段名**: 使用下划线分隔的小写字母
- **索引名**: `idx_表名_字段名` 或 `uk_表名_字段名`(唯一索引)
- **外键**: `fk_表名_引用表名_字段名`

## 3. 业务实体分析

### 3.1 核心业务实体
1. **用户管理**: 用户信息、权限控制
2. **任务管理**: SOC应答任务的全生命周期管理
3. **条目管理**: 应答条目的录入、应答、状态跟踪
4. **应答管理**: 应答结果、历史版本、AI匹配详情
5. **标签管理**: 条目分类标签系统
6. **产品管理**: 产品信息和用户权限关联
7. **数据源管理**: GBBS等外部数据源配置
8. **文件管理**: 条目导入文件、应答导出文件管理
9. **日志管理**: 操作审计和系统日志

### 3.2 实体关系分析
- 用户与任务：一对多关系
- 任务与条目：一对多关系  
- 条目与应答：一对多关系(支持历史版本)
- 条目与标签：多对多关系
- 用户与产品：多对多关系(权限控制)
- 条目与AI匹配：一对多关系

## 4. 表结构设计

### 4.1 用户管理模块

#### 4.1.1 用户表 (soc_users)
```sql
CREATE TABLE soc_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    employee_id VARCHAR(20) UNIQUE COMMENT '工号',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '部门',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0-未删除 1-已删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    INDEX idx_username (username),
    INDEX idx_employee_id (employee_id),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

#### 4.1.2 用户产品权限表 (soc_user_product_permissions)
```sql
CREATE TABLE soc_user_product_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    permission_type VARCHAR(20) DEFAULT 'read' COMMENT '权限类型：read-只读 write-读写',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    expire_time DATETIME COMMENT '过期时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    UNIQUE KEY uk_user_product (user_id, product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    CONSTRAINT fk_user_permissions_user_id FOREIGN KEY (user_id) REFERENCES soc_users (id),
    CONSTRAINT fk_user_permissions_product_id FOREIGN KEY (product_id) REFERENCES soc_products (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户产品权限表';
```

### 4.2 任务管理模块

#### 4.2.1 任务表 (soc_tasks)
```sql
CREATE TABLE soc_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country_mto VARCHAR(100) COMMENT '国家/MTO',
    mto_branch VARCHAR(100) COMMENT 'MTO分支/省公司',
    customer VARCHAR(200) COMMENT '客户',
    project VARCHAR(200) COMMENT '项目',
    data_source VARCHAR(50) DEFAULT 'GBBS' COMMENT '数据源：GBBS、文档库、项目文档、历史SOC文档',
    item_file_id BIGINT COMMENT '应答条目文件ID',
    total_items INT DEFAULT 0 COMMENT '总条目数',
    completed_items INT DEFAULT 0 COMMENT '已完成条目数',
    fc_count INT DEFAULT 0 COMMENT 'FC数量',
    pc_count INT DEFAULT 0 COMMENT 'PC数量',
    nc_count INT DEFAULT 0 COMMENT 'NC数量',
    satisfaction_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '总满足度',
    status VARCHAR(20) DEFAULT 'active' COMMENT '任务状态：active-活跃 completed-完成 archived-归档',
    is_personal TINYINT DEFAULT 0 COMMENT '是否个人任务：0-否 1-是',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0-未删除 1-已删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    INDEX idx_task_code (task_code),
    INDEX idx_task_name (task_name),
    INDEX idx_country_mto (country_mto),
    INDEX idx_customer (customer),
    INDEX idx_project (project),
    INDEX idx_created_by (created_by),
    INDEX idx_created_time (created_time),
    INDEX idx_status (status),
    CONSTRAINT fk_tasks_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id),
    CONSTRAINT fk_tasks_file_id FOREIGN KEY (item_file_id) REFERENCES soc_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';
```

### 4.3 条目管理模块

#### 4.3.1 条目表 (soc_items)
```sql
CREATE TABLE soc_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    item_description TEXT NOT NULL COMMENT '条目描述',
    additional_info TEXT COMMENT '补充信息',
    product_id BIGINT COMMENT '产品ID',
    assigned_to BIGINT COMMENT '指派给',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '应答状态：pending-未应答 processing-应答中 completed-已应答',
    satisfaction VARCHAR(10) COMMENT '满足度：FC、PC、NC',
    response_method VARCHAR(20) COMMENT '应答方式：ai-AI应答 manual-手工应答',
    response_source VARCHAR(50) COMMENT '应答来源：GBBS、文档库等',
    response_content LONGTEXT COMMENT '应答说明',
    response_index VARCHAR(500) COMMENT '索引信息',
    remark TEXT COMMENT '备注',
    auto_response TINYINT DEFAULT 1 COMMENT '自动应答：0-否 1-是',
    overwrite_on_duplicate TINYINT DEFAULT 1 COMMENT '重复时覆盖：0-否 1-是',
    similarity_score DECIMAL(5,2) COMMENT '相似度评分',
    version INT DEFAULT 1 COMMENT '版本号',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0-未删除 1-已删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    UNIQUE KEY uk_task_item_product (task_id, item_code, product_id),
    INDEX idx_item_code (item_code),
    INDEX idx_task_id (task_id),
    INDEX idx_product_id (product_id),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_status (status),
    INDEX idx_satisfaction (satisfaction),
    INDEX idx_created_time (created_time),
    CONSTRAINT fk_items_task_id FOREIGN KEY (task_id) REFERENCES soc_tasks (id),
    CONSTRAINT fk_items_product_id FOREIGN KEY (product_id) REFERENCES soc_products (id),
    CONSTRAINT fk_items_assigned_to FOREIGN KEY (assigned_to) REFERENCES soc_users (id),
    CONSTRAINT fk_items_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='条目表';
```

#### 4.3.2 条目应答历史表 (soc_item_response_history)
```sql
CREATE TABLE soc_item_response_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史记录ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    version INT NOT NULL COMMENT '版本号',
    satisfaction VARCHAR(10) COMMENT '满足度：FC、PC、NC',
    response_method VARCHAR(20) COMMENT '应答方式：ai-AI应答 manual-手工应答',
    response_source VARCHAR(50) COMMENT '应答来源',
    response_content LONGTEXT COMMENT '应答说明',
    response_index VARCHAR(500) COMMENT '索引信息',
    remark TEXT COMMENT '备注',
    operation_type VARCHAR(20) COMMENT '操作类型：create-创建 update-更新 ai_enhance-AI增强',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    
    INDEX idx_item_id (item_id),
    INDEX idx_version (version),
    INDEX idx_created_time (created_time),
    CONSTRAINT fk_history_item_id FOREIGN KEY (item_id) REFERENCES soc_items (id),
    CONSTRAINT fk_history_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='条目应答历史表';
```

### 4.4 AI匹配模块

#### 4.4.1 AI匹配结果表 (soc_ai_matches)
```sql
CREATE TABLE soc_ai_matches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源',
    match_score DECIMAL(5,2) NOT NULL COMMENT '匹配度评分',
    country_match TINYINT DEFAULT 0 COMMENT '国家匹配：0-否 1-是',
    branch_match TINYINT DEFAULT 0 COMMENT '分支匹配：0-否 1-是',
    customer_match TINYINT DEFAULT 0 COMMENT '客户匹配：0-否 1-是',
    source_item_code VARCHAR(100) COMMENT '源条目编码',
    source_description TEXT COMMENT '源条目描述',
    source_satisfaction VARCHAR(10) COMMENT '源满足度',
    source_content LONGTEXT COMMENT '源应答内容',
    source_index VARCHAR(500) COMMENT '源索引',
    source_url VARCHAR(1000) COMMENT '源链接',
    is_applied TINYINT DEFAULT 0 COMMENT '是否已应用：0-否 1-是',
    applied_time DATETIME COMMENT '应用时间',
    rank_order INT COMMENT '排序序号',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_item_id (item_id),
    INDEX idx_match_score (match_score DESC),
    INDEX idx_data_source (data_source),
    INDEX idx_created_time (created_time),
    CONSTRAINT fk_matches_item_id FOREIGN KEY (item_id) REFERENCES soc_items (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI匹配结果表';
```

### 4.5 标签管理模块

#### 4.5.1 标签表 (soc_tags)
```sql
CREATE TABLE soc_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
    tag_color VARCHAR(20) DEFAULT '#1890ff' COMMENT '标签颜色',
    tag_description VARCHAR(200) COMMENT '标签描述',
    use_count INT DEFAULT 0 COMMENT '使用次数',
    is_system TINYINT DEFAULT 0 COMMENT '是否系统标签：0-否 1-是',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    
    INDEX idx_tag_name (tag_name),
    INDEX idx_use_count (use_count DESC),
    CONSTRAINT fk_tags_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';
```

#### 4.5.2 条目标签关联表 (soc_item_tags)
```sql
CREATE TABLE soc_item_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    created_by BIGINT COMMENT '创建人ID',
    
    UNIQUE KEY uk_item_tag (item_id, tag_id),
    INDEX idx_item_id (item_id),
    INDEX idx_tag_id (tag_id),
    CONSTRAINT fk_item_tags_item_id FOREIGN KEY (item_id) REFERENCES soc_items (id) ON DELETE CASCADE,
    CONSTRAINT fk_item_tags_tag_id FOREIGN KEY (tag_id) REFERENCES soc_tags (id),
    CONSTRAINT fk_item_tags_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='条目标签关联表';
```

### 4.6 系统配置模块

#### 4.6.1 产品表 (soc_products)
```sql
CREATE TABLE soc_products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    product_type VARCHAR(50) COMMENT '产品类型',
    parent_id BIGINT COMMENT '父产品ID',
    level INT DEFAULT 1 COMMENT '层级',
    path VARCHAR(1000) COMMENT '层级路径',
    description TEXT COMMENT '产品描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0-未删除 1-已删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    INDEX idx_product_code (product_code),
    INDEX idx_product_name (product_name),
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_sort_order (sort_order),
    CONSTRAINT fk_products_parent_id FOREIGN KEY (parent_id) REFERENCES soc_products (id),
    CONSTRAINT fk_products_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';
```

#### 4.6.2 数据源配置表 (soc_data_sources)
```sql
CREATE TABLE soc_data_sources (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据源ID',
    source_code VARCHAR(50) NOT NULL UNIQUE COMMENT '数据源编码',
    source_name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    source_type VARCHAR(50) NOT NULL COMMENT '数据源类型：GBBS、文档库、项目文档、历史SOC文档',
    connection_config JSON COMMENT '连接配置',
    auth_config JSON COMMENT '认证配置',
    mapping_config JSON COMMENT '字段映射配置',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    priority INT DEFAULT 0 COMMENT '优先级',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    INDEX idx_source_code (source_code),
    INDEX idx_source_type (source_type),
    INDEX idx_priority (priority DESC),
    CONSTRAINT fk_data_sources_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据源配置表';
```

### 4.7 文件管理模块

#### 4.7.1 文件表 (soc_files)
```sql
CREATE TABLE soc_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(1000) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    business_type VARCHAR(50) COMMENT '业务类型：item_import-条目导入 response_export-应答导出',
    related_id BIGINT COMMENT '关联业务ID',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-有效 deleted-已删除',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    
    INDEX idx_file_name (file_name),
    INDEX idx_business_type (business_type),
    INDEX idx_related_id (related_id),
    INDEX idx_created_by (created_by),
    INDEX idx_created_time (created_time),
    CONSTRAINT fk_files_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';
```

### 4.8 日志管理模块

#### 4.8.1 操作日志表 (soc_operation_logs)
```sql
CREATE TABLE soc_operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT COMMENT '操作用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    business_type VARCHAR(50) COMMENT '业务类型：task-任务 item-条目 response-应答',
    business_id BIGINT COMMENT '业务对象ID',
    operation_desc VARCHAR(500) COMMENT '操作描述',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_params LONGTEXT COMMENT '请求参数',
    response_result LONGTEXT COMMENT '响应结果',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(1000) COMMENT '用户代理',
    execution_time INT COMMENT '执行时间(毫秒)',
    status VARCHAR(20) DEFAULT 'success' COMMENT '状态：success-成功 failure-失败',
    error_message TEXT COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_business_type (business_type),
    INDEX idx_business_id (business_id),
    INDEX idx_created_time (created_time),
    INDEX idx_status (status),
    CONSTRAINT fk_operation_logs_user_id FOREIGN KEY (user_id) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
```

## 5. 索引设计

### 5.1 主要索引策略
1. **主键索引**: 所有表使用自增主键，确保唯一性和查询性能
2. **唯一索引**: 业务唯一性字段（用户名、任务编码、产品编码等）
3. **复合索引**: 多字段查询场景（任务+条目+产品、用户+产品权限）
4. **外键索引**: 关联查询优化
5. **时间索引**: 支持时间范围查询和排序

### 5.2 查询优化建议
1. **分页查询**: 使用 `LIMIT + OFFSET` 配合覆盖索引
2. **模糊查询**: 条目描述等字段考虑全文索引
3. **统计查询**: 任务进度统计建议增加统计表或缓存
4. **历史数据**: 考虑按时间分区存储

## 6. 数据关系图

```mermaid
erDiagram
    soc_users ||--o{ soc_tasks : "创建"
    soc_users ||--o{ soc_items : "指派"
    soc_users }o--o{ soc_products : "权限"
    
    soc_tasks ||--o{ soc_items : "包含"
    soc_tasks }o--|| soc_files : "条目文件"
    
    soc_items ||--o{ soc_item_response_history : "历史版本"
    soc_items ||--o{ soc_ai_matches : "AI匹配"
    soc_items }o--o{ soc_tags : "标签关联"
    soc_items }o--|| soc_products : "产品"
    
    soc_tags ||--o{ soc_item_tags : "关联"
    soc_items ||--o{ soc_item_tags : "关联"
    
    soc_users ||--o{ soc_user_product_permissions : "产品权限"
    soc_products ||--o{ soc_user_product_permissions : "产品权限"
    
    soc_users ||--o{ soc_operation_logs : "操作日志"
    soc_users ||--o{ soc_files : "文件上传"
```

## 7. SQL建表语句

### 7.1 完整建表脚本
```sql
-- ================================================
-- SOC智能应答系统数据库初始化脚本
-- 版本: v1.0
-- 创建时间: 2024-12-19
-- ================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS soc_intelligent_response 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE soc_intelligent_response;

-- 1. 用户表
CREATE TABLE soc_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    employee_id VARCHAR(20) UNIQUE COMMENT '工号',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '部门',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除：0-未删除 1-已删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    INDEX idx_username (username),
    INDEX idx_employee_id (employee_id),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 产品表
CREATE TABLE soc_products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '产品ID',
    product_code VARCHAR(50) NOT NULL UNIQUE COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    product_type VARCHAR(50) COMMENT '产品类型',
    parent_id BIGINT COMMENT '父产品ID',
    level INT DEFAULT 1 COMMENT '层级',
    path VARCHAR(1000) COMMENT '层级路径',
    description TEXT COMMENT '产品描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    INDEX idx_product_code (product_code),
    INDEX idx_product_name (product_name),
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_sort_order (sort_order),
    CONSTRAINT fk_products_parent_id FOREIGN KEY (parent_id) REFERENCES soc_products (id),
    CONSTRAINT fk_products_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';

-- 3. 文件表
CREATE TABLE soc_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(1000) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    business_type VARCHAR(50) COMMENT '业务类型',
    related_id BIGINT COMMENT '关联业务ID',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    
    INDEX idx_file_name (file_name),
    INDEX idx_business_type (business_type),
    INDEX idx_related_id (related_id),
    INDEX idx_created_by (created_by),
    INDEX idx_created_time (created_time),
    CONSTRAINT fk_files_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';

-- 4. 任务表
CREATE TABLE soc_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country_mto VARCHAR(100) COMMENT '国家/MTO',
    mto_branch VARCHAR(100) COMMENT 'MTO分支',
    customer VARCHAR(200) COMMENT '客户',
    project VARCHAR(200) COMMENT '项目',
    data_source VARCHAR(50) DEFAULT 'GBBS' COMMENT '数据源',
    item_file_id BIGINT COMMENT '应答条目文件ID',
    total_items INT DEFAULT 0 COMMENT '总条目数',
    completed_items INT DEFAULT 0 COMMENT '已完成条目数',
    fc_count INT DEFAULT 0 COMMENT 'FC数量',
    pc_count INT DEFAULT 0 COMMENT 'PC数量',
    nc_count INT DEFAULT 0 COMMENT 'NC数量',
    satisfaction_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '总满足度',
    status VARCHAR(20) DEFAULT 'active' COMMENT '任务状态',
    is_personal TINYINT DEFAULT 0 COMMENT '是否个人任务',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    INDEX idx_task_code (task_code),
    INDEX idx_task_name (task_name),
    INDEX idx_country_mto (country_mto),
    INDEX idx_customer (customer),
    INDEX idx_project (project),
    INDEX idx_created_by (created_by),
    INDEX idx_created_time (created_time),
    INDEX idx_status (status),
    CONSTRAINT fk_tasks_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id),
    CONSTRAINT fk_tasks_file_id FOREIGN KEY (item_file_id) REFERENCES soc_files (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 5. 条目表
CREATE TABLE soc_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    item_description TEXT NOT NULL COMMENT '条目描述',
    additional_info TEXT COMMENT '补充信息',
    product_id BIGINT COMMENT '产品ID',
    assigned_to BIGINT COMMENT '指派给',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '应答状态',
    satisfaction VARCHAR(10) COMMENT '满足度',
    response_method VARCHAR(20) COMMENT '应答方式',
    response_source VARCHAR(50) COMMENT '应答来源',
    response_content LONGTEXT COMMENT '应答说明',
    response_index VARCHAR(500) COMMENT '索引信息',
    remark TEXT COMMENT '备注',
    auto_response TINYINT DEFAULT 1 COMMENT '自动应答',
    overwrite_on_duplicate TINYINT DEFAULT 1 COMMENT '重复时覆盖',
    similarity_score DECIMAL(5,2) COMMENT '相似度评分',
    version INT DEFAULT 1 COMMENT '版本号',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    UNIQUE KEY uk_task_item_product (task_id, item_code, product_id),
    INDEX idx_item_code (item_code),
    INDEX idx_task_id (task_id),
    INDEX idx_product_id (product_id),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_status (status),
    INDEX idx_satisfaction (satisfaction),
    INDEX idx_created_time (created_time),
    CONSTRAINT fk_items_task_id FOREIGN KEY (task_id) REFERENCES soc_tasks (id),
    CONSTRAINT fk_items_product_id FOREIGN KEY (product_id) REFERENCES soc_products (id),
    CONSTRAINT fk_items_assigned_to FOREIGN KEY (assigned_to) REFERENCES soc_users (id),
    CONSTRAINT fk_items_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='条目表';

-- 6. 标签表
CREATE TABLE soc_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
    tag_color VARCHAR(20) DEFAULT '#1890ff' COMMENT '标签颜色',
    tag_description VARCHAR(200) COMMENT '标签描述',
    use_count INT DEFAULT 0 COMMENT '使用次数',
    is_system TINYINT DEFAULT 0 COMMENT '是否系统标签',
    status TINYINT DEFAULT 1 COMMENT '状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    
    INDEX idx_tag_name (tag_name),
    INDEX idx_use_count (use_count DESC),
    CONSTRAINT fk_tags_created_by FOREIGN KEY (created_by) REFERENCES soc_users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- 继续其他表的创建...
-- (其他表的SQL语句省略，参考上面的表结构设计)
```

## 8. 数据安全和扩展性

### 8.1 数据安全措施
1. **权限控制**: 基于产品的细粒度权限控制
2. **数据加密**: 敏感字段采用AES进行加密存储
3. **审计日志**: 完整的操作审计，支持数据溯源
4. **软删除**: 关键业务数据采用软删除，保证数据可恢复
5. **备份策略**: 定期数据库备份和日志备份

### 8.2 扩展性设计
1. **字段扩展**: 预留扩展字段和JSON配置字段
2. **分表分库**: 支持按时间或业务维度进行分区
3. **缓存设计**: 热点数据缓存，减少数据库压力
4. **索引优化**: 根据业务发展调整索引策略
5. **数据归档**: 历史数据定期归档，保持系统性能

### 8.3 性能优化建议
1. **读写分离**: 查询操作使用只读从库
2. **连接池**: 合理配置数据库连接池
3. **查询优化**: 避免复杂关联查询，适当冗余设计
4. **批量操作**: 批量插入和更新优化
5. **定期维护**: 定期更新统计信息和重建索引

---

**注意事项：**
1. 本设计基于MySQL 8.0+，确保版本兼容性
2. 生产环境部署前请根据实际数据量调整索引策略
3. 敏感配置信息请使用配置中心管理，不要硬编码
4. 定期监控数据库性能，及时调整优化策略
3. [核心业务实体分析](#核心业务实体分析)
4. [数据库表设计](#数据库表设计)
5. [表关系图](#表关系图)
6. [索引设计](#索引设计)
7. [建表SQL语句](#建表sql语句)

## 概述

本文档基于SOC智能应答系统的需求文档，设计了完整的数据库表结构。系统主要用于标书应答的智能化处理，包括任务管理、条目应答、数据分析等核心功能。

### 核心业务场景
- 任务创建与管理
- 条目批量导入与应答
- AI智能应答与人工应答
- 应答结果统计分析
- 权限控制与协作

## 数据库设计原则

1. **规范化设计**：遵循第三范式，减少数据冗余
2. **性能优化**：合理设计索引，支持高频查询场景
3. **扩展性**：预留扩展字段，支持业务发展
4. **数据完整性**：通过约束保证数据一致性
5. **安全性**：敏感数据加密存储，操作日志记录

## 核心业务实体分析

### 主要业务实体
1. **任务(Task)**：SOC应答任务的基本单位
2. **条目(Item)**：需要应答的具体问题条目
3. **应答(Response)**：针对条目的具体应答内容
4. **用户(User)**：系统使用者
5. **产品(Product)**：应答涉及的产品信息
6. **标签(Tag)**：条目分类标签
7. **数据源(DataSource)**：应答数据来源
8. **匹配记录(MatchRecord)**：AI应答的匹配详情

### 实体关系
- 任务 1:N 条目
- 条目 1:N 应答（按产品维度）
- 用户 1:N 任务（创建关系）
- 用户 N:N 条目（指派关系）
- 条目 N:N 标签
- 应答 1:N 匹配记录

## 数据库表设计

### 1. 用户表 (soc_user)
存储系统用户基本信息

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 用户ID |
| user_code | VARCHAR | 50 | NOT NULL, UNIQUE | - | 用户工号 |
| user_name | VARCHAR | 100 | NOT NULL | - | 用户姓名 |
| email | VARCHAR | 200 | - | - | 邮箱地址 |
| department | VARCHAR | 100 | - | - | 部门 |
| role | VARCHAR | 50 | NOT NULL | 'USER' | 角色：ADMIN/USER |
| status | TINYINT | - | NOT NULL | 1 | 状态：1-启用，0-禁用 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 2. 任务表 (soc_task)
存储SOC应答任务信息

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 任务ID |
| task_code | VARCHAR | 50 | NOT NULL, UNIQUE | - | 任务编码 |
| task_name | VARCHAR | 200 | NOT NULL | - | 任务名称 |
| country | VARCHAR | 100 | - | - | 国家/MTO |
| mto_branch | VARCHAR | 100 | - | - | MTO分支 |
| customer | VARCHAR | 200 | - | - | 客户 |
| project | VARCHAR | 200 | - | - | 项目 |
| data_source | VARCHAR | 50 | NOT NULL | 'GBBS' | 数据源：GBBS/文档库/项目文档/历史SOC文档 |
| creator_id | BIGINT | - | NOT NULL, FK | - | 创建人ID |
| status | TINYINT | - | NOT NULL | 1 | 状态：1-进行中，2-已完成，0-已删除 |
| total_items | INT | - | NOT NULL | 0 | 总条目数 |
| answered_items | INT | - | NOT NULL | 0 | 已应答条目数 |
| fc_count | INT | - | NOT NULL | 0 | FC数量 |
| pc_count | INT | - | NOT NULL | 0 | PC数量 |
| nc_count | INT | - | NOT NULL | 0 | NC数量 |
| satisfaction_rate | DECIMAL | 5,2 | NOT NULL | 0.00 | 总满足度(%) |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 3. 产品表 (soc_product)
存储产品信息

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 产品ID |
| product_code | VARCHAR | 100 | NOT NULL, UNIQUE | - | 产品编码 |
| product_name | VARCHAR | 200 | NOT NULL | - | 产品名称 |
| product_type | VARCHAR | 50 | - | - | 产品类型 |
| parent_id | BIGINT | - | FK | - | 父产品ID |
| level | TINYINT | - | NOT NULL | 1 | 层级 |
| sort_order | INT | - | NOT NULL | 0 | 排序 |
| status | TINYINT | - | NOT NULL | 1 | 状态：1-启用，0-禁用 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 4. 标签表 (soc_tag)
存储条目标签信息

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 标签ID |
| tag_name | VARCHAR | 100 | NOT NULL, UNIQUE | - | 标签名称 |
| tag_color | VARCHAR | 20 | - | '#1890ff' | 标签颜色 |
| usage_count | INT | - | NOT NULL | 0 | 使用次数 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 5. 条目表 (soc_item)
存储应答条目信息

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 条目ID |
| task_id | BIGINT | - | NOT NULL, FK | - | 任务ID |
| item_code | VARCHAR | 100 | NOT NULL | - | 条目编号 |
| item_description | TEXT | - | NOT NULL | - | 条目描述 |
| supplement_info | TEXT | - | - | - | 补充信息 |
| status | TINYINT | - | NOT NULL | 1 | 应答状态：1-未应答，2-应答中，3-已应答 |
| auto_response | TINYINT | - | NOT NULL | 1 | 是否自动应答：1-是，0-否 |
| assignee_id | BIGINT | - | FK | - | 指派给用户ID |
| remark | TEXT | - | - | - | 备注 |
| created_by | BIGINT | - | NOT NULL, FK | - | 创建人ID |
| updated_by | BIGINT | - | FK | - | 最后更新人ID |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 6. 应答表 (soc_response)
存储条目应答结果（按产品维度）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 应答ID |
| item_id | BIGINT | - | NOT NULL, FK | - | 条目ID |
| product_id | BIGINT | - | NOT NULL, FK | - | 产品ID |
| satisfaction | VARCHAR | 10 | - | - | 满足度：FC/PC/NC |
| response_content | LONGTEXT | - | - | - | 应答说明（富文本） |
| response_method | VARCHAR | 20 | - | - | 应答方式：AI/手工 |
| data_source | VARCHAR | 50 | - | - | 应答来源 |
| source_index | VARCHAR | 500 | - | - | 索引信息 |
| match_score | DECIMAL | 5,2 | - | - | 匹配度分数 |
| status | TINYINT | - | NOT NULL | 1 | 状态：1-进行中，2-已完成 |
| version | INT | - | NOT NULL | 1 | 版本号 |
| created_by | BIGINT | - | NOT NULL, FK | - | 创建人ID |
| updated_by | BIGINT | - | FK | - | 最后更新人ID |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

### 7. 条目标签关联表 (soc_item_tag)
存储条目与标签的多对多关系

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 关联ID |
| item_id | BIGINT | - | NOT NULL, FK | - | 条目ID |
| tag_id | BIGINT | - | NOT NULL, FK | - | 标签ID |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

### 8. 匹配记录表 (soc_match_record)
存储AI应答的匹配详情

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 匹配记录ID |
| response_id | BIGINT | - | NOT NULL, FK | - | 应答ID |
| data_source | VARCHAR | 50 | NOT NULL | - | 数据源 |
| source_item_code | VARCHAR | 100 | - | - | 源条目编号 |
| source_description | TEXT | - | - | - | 源条目描述 |
| source_satisfaction | VARCHAR | 10 | - | - | 源满足度 |
| source_content | LONGTEXT | - | - | - | 源应答内容 |
| source_index | VARCHAR | 500 | - | - | 源索引 |
| match_score | DECIMAL | 5,2 | NOT NULL | 0.00 | 匹配度分数 |
| country_match | TINYINT | - | NOT NULL | 0 | 国家匹配：1-匹配，0-不匹配 |
| branch_match | TINYINT | - | NOT NULL | 0 | 分支匹配：1-匹配，0-不匹配 |
| customer_match | TINYINT | - | NOT NULL | 0 | 客户匹配：1-匹配，0-不匹配 |
| is_selected | TINYINT | - | NOT NULL | 0 | 是否被选中：1-是，0-否 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

### 9. 应答历史表 (soc_response_history)
存储应答的历史版本

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 历史记录ID |
| response_id | BIGINT | - | NOT NULL, FK | - | 应答ID |
| version | INT | - | NOT NULL | - | 版本号 |
| satisfaction | VARCHAR | 10 | - | - | 满足度 |
| response_content | LONGTEXT | - | - | - | 应答说明 |
| response_method | VARCHAR | 20 | - | - | 应答方式 |
| data_source | VARCHAR | 50 | - | - | 应答来源 |
| source_index | VARCHAR | 500 | - | - | 索引信息 |
| change_reason | VARCHAR | 200 | - | - | 变更原因 |
| created_by | BIGINT | - | NOT NULL, FK | - | 创建人ID |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

### 10. 操作日志表 (soc_operation_log)
记录系统操作日志

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 日志ID |
| user_id | BIGINT | - | NOT NULL, FK | - | 操作用户ID |
| operation_type | VARCHAR | 50 | NOT NULL | - | 操作类型 |
| operation_desc | VARCHAR | 500 | - | - | 操作描述 |
| target_type | VARCHAR | 50 | - | - | 目标类型：TASK/ITEM/RESPONSE |
| target_id | BIGINT | - | - | - | 目标ID |
| request_params | JSON | - | - | - | 请求参数 |
| ip_address | VARCHAR | 50 | - | - | IP地址 |
| user_agent | VARCHAR | 500 | - | - | 用户代理 |
| execution_time | INT | - | - | - | 执行时间(ms) |
| status | TINYINT | - | NOT NULL | 1 | 状态：1-成功，0-失败 |
| error_message | TEXT | - | - | - | 错误信息 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

## 表关系图

### ER图说明
- **一对多关系**：用户创建多个任务，任务包含多个条目，条目有多个应答（按产品维度）
- **多对多关系**：条目与标签的关联关系
- **继承关系**：产品表支持层级结构
- **历史记录**：应答支持版本管理

### 核心关系约束
1. **任务-条目**：一个任务包含多个条目，删除任务时级联删除条目
2. **条目-应答**：一个条目按产品维度有多个应答，条目删除时级联删除应答
3. **应答-匹配记录**：一个应答对应多个匹配记录，应答删除时级联删除匹配记录
4. **应答-历史版本**：应答修改时自动创建历史版本
5. **用户权限**：任务创建人和条目指派人有相应操作权限

## 索引设计

### 主要索引策略
基于系统的查询场景和性能要求，设计以下索引：

#### 1. 任务表索引
```sql
-- 主键索引（自动创建）
PRIMARY KEY (id)

-- 唯一索引
UNIQUE KEY uk_task_code (task_code)

-- 复合索引（支持任务列表查询）
KEY idx_task_creator_status (creator_id, status, created_time DESC)

-- 单列索引
KEY idx_task_country (country)
KEY idx_task_customer (customer)
KEY idx_task_project (project)
KEY idx_task_created_time (created_time)
```

#### 2. 条目表索引
```sql
-- 主键索引（自动创建）
PRIMARY KEY (id)

-- 复合索引（支持条目列表查询）
KEY idx_item_task_status (task_id, status, updated_time DESC)
KEY idx_item_assignee_status (assignee_id, status, updated_time DESC)

-- 单列索引
KEY idx_item_code (item_code)
KEY idx_item_assignee (assignee_id)
KEY idx_item_created_by (created_by)
KEY idx_item_updated_by (updated_by)
```

#### 3. 应答表索引
```sql
-- 主键索引（自动创建）
PRIMARY KEY (id)

-- 复合索引（支持应答查询）
KEY idx_response_item_product (item_id, product_id, version DESC)
KEY idx_response_satisfaction (satisfaction, created_time DESC)

-- 单列索引
KEY idx_response_item (item_id)
KEY idx_response_product (product_id)
KEY idx_response_method (response_method)
KEY idx_response_created_by (created_by)
```

#### 4. 匹配记录表索引
```sql
-- 主键索引（自动创建）
PRIMARY KEY (id)

-- 复合索引（支持匹配记录查询）
KEY idx_match_response_score (response_id, match_score DESC)
KEY idx_match_source_score (data_source, match_score DESC)

-- 单列索引
KEY idx_match_response (response_id)
KEY idx_match_selected (is_selected)
```

#### 5. 其他表索引
```sql
-- 用户表
KEY idx_user_code (user_code)
KEY idx_user_status (status)

-- 产品表
KEY idx_product_code (product_code)
KEY idx_product_parent (parent_id, sort_order)
KEY idx_product_status (status)

-- 标签表
KEY idx_tag_name (tag_name)
KEY idx_tag_usage (usage_count DESC)

-- 条目标签关联表
KEY idx_item_tag_item (item_id)
KEY idx_item_tag_tag (tag_id)
UNIQUE KEY uk_item_tag (item_id, tag_id)

-- 应答历史表
KEY idx_history_response_version (response_id, version DESC)
KEY idx_history_created_by (created_by)

-- 操作日志表
KEY idx_log_user_time (user_id, created_time DESC)
KEY idx_log_type_time (operation_type, created_time DESC)
KEY idx_log_target (target_type, target_id)
```

### 索引优化建议
1. **分区策略**：对于日志表，建议按月分区以提高查询性能
2. **覆盖索引**：对于频繁查询的字段组合，考虑创建覆盖索引
3. **索引监控**：定期监控索引使用情况，删除无用索引
4. **统计信息**：定期更新表统计信息以优化查询计划

## 建表SQL语句

### 1. 创建数据库
```sql
CREATE DATABASE soc_intelligent_response
DEFAULT CHARACTER SET utf8mb4
DEFAULT COLLATE utf8mb4_unicode_ci;

USE soc_intelligent_response;
```

### 2. 用户表
```sql
CREATE TABLE soc_user (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    user_code VARCHAR(50) NOT NULL COMMENT '用户工号',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    email VARCHAR(200) DEFAULT NULL COMMENT '邮箱地址',
    department VARCHAR(100) DEFAULT NULL COMMENT '部门',
    role VARCHAR(50) NOT NULL DEFAULT 'USER' COMMENT '角色：ADMIN/USER',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_code (user_code),
    KEY idx_user_status (status),
    KEY idx_user_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 3. 任务表
```sql
CREATE TABLE soc_task (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL COMMENT '任务编码',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country VARCHAR(100) DEFAULT NULL COMMENT '国家/MTO',
    mto_branch VARCHAR(100) DEFAULT NULL COMMENT 'MTO分支',
    customer VARCHAR(200) DEFAULT NULL COMMENT '客户',
    project VARCHAR(200) DEFAULT NULL COMMENT '项目',
    data_source VARCHAR(50) NOT NULL DEFAULT 'GBBS' COMMENT '数据源：GBBS/文档库/项目文档/历史SOC文档',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-进行中，2-已完成，0-已删除',
    total_items INT NOT NULL DEFAULT 0 COMMENT '总条目数',
    answered_items INT NOT NULL DEFAULT 0 COMMENT '已应答条目数',
    fc_count INT NOT NULL DEFAULT 0 COMMENT 'FC数量',
    pc_count INT NOT NULL DEFAULT 0 COMMENT 'PC数量',
    nc_count INT NOT NULL DEFAULT 0 COMMENT 'NC数量',
    satisfaction_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '总满足度(%)',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_task_code (task_code),
    KEY idx_task_creator_status (creator_id, status, created_time DESC),
    KEY idx_task_country (country),
    KEY idx_task_customer (customer),
    KEY idx_task_project (project),
    KEY idx_task_created_time (created_time),
    CONSTRAINT fk_task_creator FOREIGN KEY (creator_id) REFERENCES soc_user (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';
```

### 4. 产品表
```sql
CREATE TABLE soc_product (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '产品ID',
    product_code VARCHAR(100) NOT NULL COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    product_type VARCHAR(50) DEFAULT NULL COMMENT '产品类型',
    parent_id BIGINT DEFAULT NULL COMMENT '父产品ID',
    level TINYINT NOT NULL DEFAULT 1 COMMENT '层级',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_product_code (product_code),
    KEY idx_product_parent (parent_id, sort_order),
    KEY idx_product_status (status),
    KEY idx_product_created_time (created_time),
    CONSTRAINT fk_product_parent FOREIGN KEY (parent_id) REFERENCES soc_product (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';
```

### 5. 标签表
```sql
CREATE TABLE soc_tag (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    tag_color VARCHAR(20) DEFAULT '#1890ff' COMMENT '标签颜色',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_tag_name (tag_name),
    KEY idx_tag_usage (usage_count DESC),
    KEY idx_tag_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

### 6. 条目表
```sql
CREATE TABLE soc_item (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    item_description TEXT NOT NULL COMMENT '条目描述',
    supplement_info TEXT DEFAULT NULL COMMENT '补充信息',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '应答状态：1-未应答，2-应答中，3-已应答',
    auto_response TINYINT NOT NULL DEFAULT 1 COMMENT '是否自动应答：1-是，0-否',
    assignee_id BIGINT DEFAULT NULL COMMENT '指派给用户ID',
    remark TEXT DEFAULT NULL COMMENT '备注',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    updated_by BIGINT DEFAULT NULL COMMENT '最后更新人ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_item_task_status (task_id, status, updated_time DESC),
    KEY idx_item_assignee_status (assignee_id, status, updated_time DESC),
    KEY idx_item_code (item_code),
    KEY idx_item_assignee (assignee_id),
    KEY idx_item_created_by (created_by),
    KEY idx_item_updated_by (updated_by),
    CONSTRAINT fk_item_task FOREIGN KEY (task_id) REFERENCES soc_task (id) ON DELETE CASCADE,
    CONSTRAINT fk_item_assignee FOREIGN KEY (assignee_id) REFERENCES soc_user (id),
    CONSTRAINT fk_item_created_by FOREIGN KEY (created_by) REFERENCES soc_user (id),
    CONSTRAINT fk_item_updated_by FOREIGN KEY (updated_by) REFERENCES soc_user (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='条目表';
```

### 7. 应答表
```sql
CREATE TABLE soc_response (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '应答ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    satisfaction VARCHAR(10) DEFAULT NULL COMMENT '满足度：FC/PC/NC',
    response_content LONGTEXT DEFAULT NULL COMMENT '应答说明（富文本）',
    response_method VARCHAR(20) DEFAULT NULL COMMENT '应答方式：AI/手工',
    data_source VARCHAR(50) DEFAULT NULL COMMENT '应答来源',
    source_index VARCHAR(500) DEFAULT NULL COMMENT '索引信息',
    match_score DECIMAL(5,2) DEFAULT NULL COMMENT '匹配度分数',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-进行中，2-已完成',
    version INT NOT NULL DEFAULT 1 COMMENT '版本号',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    updated_by BIGINT DEFAULT NULL COMMENT '最后更新人ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_response_item_product (item_id, product_id, version DESC),
    KEY idx_response_satisfaction (satisfaction, created_time DESC),
    KEY idx_response_item (item_id),
    KEY idx_response_product (product_id),
    KEY idx_response_method (response_method),
    KEY idx_response_created_by (created_by),
    UNIQUE KEY uk_response_item_product_version (item_id, product_id, version),
    CONSTRAINT fk_response_item FOREIGN KEY (item_id) REFERENCES soc_item (id) ON DELETE CASCADE,
    CONSTRAINT fk_response_product FOREIGN KEY (product_id) REFERENCES soc_product (id),
    CONSTRAINT fk_response_created_by FOREIGN KEY (created_by) REFERENCES soc_user (id),
    CONSTRAINT fk_response_updated_by FOREIGN KEY (updated_by) REFERENCES soc_user (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应答表';
```

### 8. 条目标签关联表
```sql
CREATE TABLE soc_item_tag (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_item_tag_item (item_id),
    KEY idx_item_tag_tag (tag_id),
    UNIQUE KEY uk_item_tag (item_id, tag_id),
    CONSTRAINT fk_item_tag_item FOREIGN KEY (item_id) REFERENCES soc_item (id) ON DELETE CASCADE,
    CONSTRAINT fk_item_tag_tag FOREIGN KEY (tag_id) REFERENCES soc_tag (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='条目标签关联表';
```

### 9. 匹配记录表
```sql
CREATE TABLE soc_match_record (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '匹配记录ID',
    response_id BIGINT NOT NULL COMMENT '应答ID',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源',
    source_item_code VARCHAR(100) DEFAULT NULL COMMENT '源条目编号',
    source_description TEXT DEFAULT NULL COMMENT '源条目描述',
    source_satisfaction VARCHAR(10) DEFAULT NULL COMMENT '源满足度',
    source_content LONGTEXT DEFAULT NULL COMMENT '源应答内容',
    source_index VARCHAR(500) DEFAULT NULL COMMENT '源索引',
    match_score DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '匹配度分数',
    country_match TINYINT NOT NULL DEFAULT 0 COMMENT '国家匹配：1-匹配，0-不匹配',
    branch_match TINYINT NOT NULL DEFAULT 0 COMMENT '分支匹配：1-匹配，0-不匹配',
    customer_match TINYINT NOT NULL DEFAULT 0 COMMENT '客户匹配：1-匹配，0-不匹配',
    is_selected TINYINT NOT NULL DEFAULT 0 COMMENT '是否被选中：1-是，0-否',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_match_response_score (response_id, match_score DESC),
    KEY idx_match_source_score (data_source, match_score DESC),
    KEY idx_match_response (response_id),
    KEY idx_match_selected (is_selected),
    CONSTRAINT fk_match_response FOREIGN KEY (response_id) REFERENCES soc_response (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配记录表';

### 10. 应答历史表
```sql
CREATE TABLE soc_response_history (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
    response_id BIGINT NOT NULL COMMENT '应答ID',
    version INT NOT NULL COMMENT '版本号',
    satisfaction VARCHAR(10) DEFAULT NULL COMMENT '满足度',
    response_content LONGTEXT DEFAULT NULL COMMENT '应答说明',
    response_method VARCHAR(20) DEFAULT NULL COMMENT '应答方式',
    data_source VARCHAR(50) DEFAULT NULL COMMENT '应答来源',
    source_index VARCHAR(500) DEFAULT NULL COMMENT '索引信息',
    change_reason VARCHAR(200) DEFAULT NULL COMMENT '变更原因',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_history_response_version (response_id, version DESC),
    KEY idx_history_created_by (created_by),
    KEY idx_history_created_time (created_time),
    CONSTRAINT fk_history_response FOREIGN KEY (response_id) REFERENCES soc_response (id) ON DELETE CASCADE,
    CONSTRAINT fk_history_created_by FOREIGN KEY (created_by) REFERENCES soc_user (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应答历史表';
```

### 11. 操作日志表
```sql
CREATE TABLE soc_operation_log (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(500) DEFAULT NULL COMMENT '操作描述',
    target_type VARCHAR(50) DEFAULT NULL COMMENT '目标类型：TASK/ITEM/RESPONSE',
    target_id BIGINT DEFAULT NULL COMMENT '目标ID',
    request_params JSON DEFAULT NULL COMMENT '请求参数',
    ip_address VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    user_agent VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    execution_time INT DEFAULT NULL COMMENT '执行时间(ms)',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-成功，0-失败',
    error_message TEXT DEFAULT NULL COMMENT '错误信息',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_log_user_time (user_id, created_time DESC),
    KEY idx_log_type_time (operation_type, created_time DESC),
    KEY idx_log_target (target_type, target_id),
    KEY idx_log_status (status),
    CONSTRAINT fk_log_user FOREIGN KEY (user_id) REFERENCES soc_user (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
```

### 12. 初始化数据
```sql
-- 插入默认产品数据
INSERT INTO soc_product (product_code, product_name, product_type, level, sort_order) VALUES
('SOC_STANDARD', 'SOC标准库', 'STANDARD', 1, 1),
('SOC_ACCUMULATE', 'SOC积累库', 'ACCUMULATE', 1, 2),
('PRODUCT_TREE', '产品目录树', 'PRODUCT', 1, 3),
('HUAWEI_CLOUD_STACK', '华为云Stack', 'PRODUCT', 2, 1),
('FUSION_SPHERE', 'FusionSphere', 'PRODUCT', 2, 2),
('CLOUD_SECURITY', '云安全服务', 'PRODUCT', 2, 3),
('FUSION_COMPUTE', 'FusionCompute', 'PRODUCT', 2, 4);

-- 插入默认标签数据
INSERT INTO soc_tag (tag_name, tag_color) VALUES
('安全', '#f50'),
('性能', '#2db7f5'),
('功能', '#87d068'),
('兼容性', '#108ee9'),
('可靠性', '#f56a00'),
('扩展性', '#722ed1');
```

## 数据安全与扩展性考虑

### 数据安全
1. **敏感数据加密**：用户密码、关键配置信息采用AES加密存储
2. **访问控制**：基于角色的权限控制，确保数据访问安全
3. **操作审计**：完整的操作日志记录，支持安全审计
4. **数据备份**：定期数据备份，支持灾难恢复

### 扩展性设计
1. **水平扩展**：支持读写分离、分库分表
2. **版本管理**：应答内容支持版本控制
3. **插件化**：数据源支持插件化扩展
4. **缓存策略**：热点数据缓存，提升系统性能

### 性能优化建议
1. **分页查询**：大数据量查询采用分页处理
2. **异步处理**：AI应答等耗时操作采用异步处理
3. **连接池**：数据库连接池优化
4. **监控告警**：完善的性能监控和告警机制

---

**文档版本**：v1.0
**创建时间**：2024年
**更新时间**：2024年
**维护人员**：SOC智能应答系统开发团队
```
```

