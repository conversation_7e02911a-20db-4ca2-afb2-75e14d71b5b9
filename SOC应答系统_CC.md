# SOC智能应答系统 - 表结构与接口设计

## 1. 系统概述

SOC智能应答系统是一个基于AI技术的标书应答自动化平台，主要解决传统标书应答流程中效率低、质量不稳定、知识复用度低等问题。系统支持多数据源（GBBS、文档库、项目文档、历史SOC文档）的智能匹配和应答生成。

### 1.1 核心术语
- **条目(Item)**：从标书中梳理出来的要求/问题
- **应答(Response)**：对条目的回答，通常以产品维度对条目进行应答
- **SOC**：Statement of Compliance，符合性声明
- **FC**：Full Compliance 完全满足
- **PC**：Partially Compliance 部分满足  
- **NC**：Not Compliance 不满足

### 1.2 系统架构
系统采用前后端分离架构，支持RESTful API接口，具备完整的权限管理、任务管理、条目管理、AI应答和数据分析功能。

## 2. 数据库设计

### 2.1 数据库表结构

#### 2.1.1 用户管理相关表

**用户表 (soc_users)**
```sql
CREATE TABLE soc_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    user_code VARCHAR(50) NOT NULL UNIQUE COMMENT '用户工号',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    email VARCHAR(200) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    department VARCHAR(100) COMMENT '部门',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_code (user_code),
    INDEX idx_user_name (user_name)
) COMMENT='用户表';
```

**用户权限表 (soc_user_permissions)**
```sql
CREATE TABLE soc_user_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    permission_type VARCHAR(50) NOT NULL COMMENT '权限类型：SOC_USER-普通用户',
    product_permissions TEXT COMMENT '产品权限JSON，存储用户可访问的产品列表',
    status TINYINT DEFAULT 1 COMMENT '状态：1-有效，0-无效',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES soc_users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_permission_type (permission_type)
) COMMENT='用户权限表';
```

#### 2.1.2 任务管理相关表

**任务表 (soc_tasks)**
```sql
CREATE TABLE soc_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码，系统自动生成',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country_mto VARCHAR(100) COMMENT '国家/MTO',
    mto_branch VARCHAR(100) COMMENT 'MTO分支/省公司',
    customer VARCHAR(200) COMMENT '客户',
    project VARCHAR(200) COMMENT '项目',
    data_source VARCHAR(50) DEFAULT 'GBBS' COMMENT '数据源：GBBS、文档库、项目文档、历史SOC文档',
    task_type TINYINT DEFAULT 1 COMMENT '任务类型：1-普通任务，2-个人任务（快捷应答）',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    updated_by BIGINT COMMENT '更新人ID',
    FOREIGN KEY (creator_id) REFERENCES soc_users(id),
    FOREIGN KEY (updated_by) REFERENCES soc_users(id),
    INDEX idx_task_code (task_code),
    INDEX idx_task_name (task_name),
    INDEX idx_creator_id (creator_id),
    INDEX idx_country_mto (country_mto),
    INDEX idx_customer (customer),
    INDEX idx_project (project),
    INDEX idx_task_type (task_type)
) COMMENT='任务表';
```

#### 2.1.3 条目管理相关表

**条目表 (soc_items)**
```sql
CREATE TABLE soc_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    item_description TEXT NOT NULL COMMENT '条目描述',
    supplement_info TEXT COMMENT '补充信息，用于AI应答时的额外上下文',
    assigned_to BIGINT COMMENT '指派给用户ID',
    auto_answer TINYINT DEFAULT 1 COMMENT '自动应答：1-是，0-否',
    overwrite_on_duplicate TINYINT DEFAULT 1 COMMENT '重复时覆盖：1-是，0-否',
    remarks TEXT COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人ID',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (task_id) REFERENCES soc_tasks(id),
    FOREIGN KEY (assigned_to) REFERENCES soc_users(id),
    FOREIGN KEY (created_by) REFERENCES soc_users(id),
    FOREIGN KEY (updated_by) REFERENCES soc_users(id),
    UNIQUE KEY uk_task_item (task_id, item_code),
    INDEX idx_task_id (task_id),
    INDEX idx_item_code (item_code),
    INDEX idx_assigned_to (assigned_to)
) COMMENT='条目表';
```

**条目应答表 (soc_item_responses)**
```sql
CREATE TABLE soc_item_responses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '应答ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    product VARCHAR(200) NOT NULL COMMENT '产品',
    response_status TINYINT DEFAULT 0 COMMENT '应答状态：0-未应答，1-应答中，2-已应答',
    compliance_level VARCHAR(10) COMMENT '满足度：FC-完全满足，PC-部分满足，NC-不满足',
    response_content LONGTEXT COMMENT '应答说明，支持富文本和图片',
    response_method VARCHAR(20) COMMENT '应答方式：AI、手工',
    response_source VARCHAR(50) COMMENT '应答来源：GBBS、文档库等',
    source_index VARCHAR(500) COMMENT '索引信息，如GBBS链接、文档章节等',
    match_score DECIMAL(5,2) COMMENT '匹配度分数',
    version INT DEFAULT 1 COMMENT '版本号，用于历史版本管理',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人ID',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (item_id) REFERENCES soc_items(id),
    FOREIGN KEY (created_by) REFERENCES soc_users(id),
    FOREIGN KEY (updated_by) REFERENCES soc_users(id),
    UNIQUE KEY uk_item_product (item_id, product),
    INDEX idx_item_id (item_id),
    INDEX idx_product (product),
    INDEX idx_response_status (response_status),
    INDEX idx_compliance_level (compliance_level),
    INDEX idx_response_method (response_method),
    INDEX idx_response_source (response_source)
) COMMENT='条目应答表';
```

#### 2.1.4 AI匹配相关表

**AI匹配结果表 (soc_ai_matches)**
```sql
CREATE TABLE soc_ai_matches (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    product VARCHAR(200) NOT NULL COMMENT '产品',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源：GBBS、文档库等',
    source_item_id VARCHAR(100) COMMENT '源条目ID',
    source_description TEXT COMMENT '源条目描述',
    match_score DECIMAL(5,2) NOT NULL COMMENT '匹配度分数(0-100)',
    country_match TINYINT DEFAULT 0 COMMENT '国家匹配：1-匹配，0-不匹配',
    branch_match TINYINT DEFAULT 0 COMMENT '分支匹配：1-匹配，0-不匹配',
    customer_match TINYINT DEFAULT 0 COMMENT '客户匹配：1-匹配，0-不匹配',
    compliance_level VARCHAR(10) COMMENT '满足度：FC、PC、NC',
    response_content LONGTEXT COMMENT '应答内容',
    source_index VARCHAR(500) COMMENT '源索引',
    is_selected TINYINT DEFAULT 0 COMMENT '是否被选中应用：1-是，0-否',
    match_rank INT COMMENT '匹配排名',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (item_id) REFERENCES soc_items(id),
    INDEX idx_item_product (item_id, product),
    INDEX idx_match_score (match_score),
    INDEX idx_data_source (data_source),
    INDEX idx_is_selected (is_selected),
    INDEX idx_match_rank (match_rank)
) COMMENT='AI匹配结果表';
```

#### 2.1.5 标签管理相关表

**标签表 (soc_tags)**
```sql
CREATE TABLE soc_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    tag_color VARCHAR(20) DEFAULT '#1890ff' COMMENT '标签颜色',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    created_by BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (created_by) REFERENCES soc_users(id),
    UNIQUE KEY uk_tag_name (tag_name),
    INDEX idx_usage_count (usage_count),
    INDEX idx_created_by (created_by)
) COMMENT='标签表';
```

**条目标签关联表 (soc_item_tags)**
```sql
CREATE TABLE soc_item_tags (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (item_id) REFERENCES soc_items(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES soc_tags(id) ON DELETE CASCADE,
    UNIQUE KEY uk_item_tag (item_id, tag_id),
    INDEX idx_item_id (item_id),
    INDEX idx_tag_id (tag_id)
) COMMENT='条目标签关联表';
```

#### 2.1.6 系统配置相关表

**数据源配置表 (soc_data_sources)**
```sql
CREATE TABLE soc_data_sources (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据源ID',
    source_code VARCHAR(50) NOT NULL UNIQUE COMMENT '数据源编码',
    source_name VARCHAR(100) NOT NULL COMMENT '数据源名称',
    source_type VARCHAR(50) NOT NULL COMMENT '数据源类型：GBBS、文档库、项目文档、历史SOC文档',
    config_json TEXT COMMENT '配置信息JSON',
    api_endpoint VARCHAR(500) COMMENT 'API接口地址',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_source_code (source_code),
    INDEX idx_source_type (source_type),
    INDEX idx_status (status)
) COMMENT='数据源配置表';
```

**产品目录表 (soc_products)**
```sql
CREATE TABLE soc_products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '产品ID',
    product_code VARCHAR(100) NOT NULL UNIQUE COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    parent_id BIGINT COMMENT '父级产品ID',
    product_level INT DEFAULT 1 COMMENT '产品层级',
    product_path VARCHAR(1000) COMMENT '产品路径，用/分隔',
    category VARCHAR(100) COMMENT '产品分类：SOC标准库、SOC积累库、产品目录树',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_id) REFERENCES soc_products(id),
    INDEX idx_product_code (product_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_category (category),
    INDEX idx_product_level (product_level),
    INDEX idx_sort_order (sort_order)
) COMMENT='产品目录表';
```

#### 2.1.7 文件管理相关表

**文件上传表 (soc_files)**
```sql
CREATE TABLE soc_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    file_size BIGINT COMMENT '文件大小(字节)',
    file_type VARCHAR(50) COMMENT '文件类型：xlsx、docx等',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    upload_user_id BIGINT NOT NULL COMMENT '上传用户ID',
    related_type VARCHAR(50) COMMENT '关联类型：TASK_IMPORT-任务导入，RESPONSE_IMAGE-应答图片',
    related_id BIGINT COMMENT '关联对象ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-删除',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (upload_user_id) REFERENCES soc_users(id),
    INDEX idx_upload_user (upload_user_id),
    INDEX idx_related (related_type, related_id),
    INDEX idx_file_name (file_name),
    INDEX idx_created_time (created_time)
) COMMENT='文件上传表';
```

#### 2.1.8 相似度分析相关表

**条目相似度表 (soc_item_similarity)**
```sql
CREATE TABLE soc_item_similarity (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '相似度ID',
    source_item_id BIGINT NOT NULL COMMENT '源条目ID',
    target_item_id BIGINT NOT NULL COMMENT '目标条目ID',
    similarity_score DECIMAL(5,4) NOT NULL COMMENT '相似度分数(0-1)',
    similarity_type VARCHAR(50) DEFAULT 'CONTENT' COMMENT '相似度类型：CONTENT-内容相似，TAG-标签相似',
    algorithm VARCHAR(50) COMMENT '相似度算法：COSINE、JACCARD等',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (source_item_id) REFERENCES soc_items(id),
    FOREIGN KEY (target_item_id) REFERENCES soc_items(id),
    INDEX idx_source_item (source_item_id),
    INDEX idx_target_item (target_item_id),
    INDEX idx_similarity_score (similarity_score),
    INDEX idx_similarity_type (similarity_type)
) COMMENT='条目相似度表';
```

#### 2.1.9 操作日志相关表

**操作日志表 (soc_operation_logs)**
```sql
CREATE TABLE soc_operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型：CREATE、UPDATE、DELETE、QUERY、AI_ANSWER等',
    operation_module VARCHAR(50) NOT NULL COMMENT '操作模块：TASK、ITEM、RESPONSE、USER等',
    target_id BIGINT COMMENT '目标对象ID',
    operation_content TEXT COMMENT '操作内容JSON',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    operation_result TINYINT DEFAULT 1 COMMENT '操作结果：1-成功，0-失败',
    error_message TEXT COMMENT '错误信息',
    operation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    FOREIGN KEY (user_id) REFERENCES soc_users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_module (operation_module),
    INDEX idx_operation_time (operation_time),
    INDEX idx_operation_result (operation_result)
) COMMENT='操作日志表';
```

### 2.2 数据字典

#### 2.2.1 应答状态枚举
- `0`: 未应答 - 条目尚未开始应答
- `1`: 应答中 - 条目正在进行AI匹配或等待人工应答
- `2`: 已应答 - 条目已完成应答

#### 2.2.2 满足度枚举
- `FC`: Full Compliance - 完全满足
- `PC`: Partially Compliance - 部分满足
- `NC`: Not Compliance - 不满足

#### 2.2.3 应答方式枚举
- `AI`: AI自动应答
- `手工`: 人工应答

#### 2.2.4 数据源类型枚举
- `GBBS`: GBBS系统
- `文档库`: 文档库系统  
- `项目文档`: 项目文档
- `历史SOC文档`: 历史SOC文档

#### 2.2.5 任务类型枚举
- `1`: 普通任务 - 正常创建的任务
- `2`: 个人任务 - 通过快捷应答创建的个人任务

### 2.3 初始化数据

```sql
-- 初始化数据源配置
INSERT INTO soc_data_sources (source_code, source_name, source_type, status) VALUES
('GBBS', 'GBBS系统', 'GBBS', 1),
('DOC_LIB', '文档库', '文档库', 1),
('PROJECT_DOC', '项目文档', '项目文档', 1),
('HISTORY_SOC', '历史SOC文档', '历史SOC文档', 1);

-- 初始化产品目录
INSERT INTO soc_products (product_code, product_name, parent_id, product_level, category, sort_order) VALUES
('SOC_STD', 'SOC标准库', NULL, 1, 'SOC标准库', 1),
('SOC_ACC', 'SOC积累库', NULL, 1, 'SOC积累库', 2),
('PRODUCT_TREE', '产品目录树', NULL,1, '产品目录树', 3);
```

## 3. API接口设计

### 3.1 接口设计原则

- 遵循RESTful设计规范
- 统一的JSON响应格式
- 完善的错误处理机制
- 支持分页查询
- 接口版本控制 (v1)
- JWT Token认证
- 基于权限的访问控制

### 3.2 通用响应格式

#### 3.2.1 成功响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 3.2.2 错误响应格式
```json
{
    "code": 400,
    "message": "参数错误",
    "error": "详细错误信息",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 3.2.3 分页响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [],
        "pagination": {
            "current": 1,
            "pageSize": 20,
            "total": 100,
            "totalPages": 5
        }
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 3.3 用户认证接口

#### 3.3.1 用户登录
```
POST /api/v1/auth/login
```

**请求参数：**
```json
{
    "userCode": "string",     // 用户工号
    "password": "string"      // 密码
}
```

**响应数据：**
```json
{
    "token": "jwt_token_string",
    "userInfo": {
        "id": 1,
        "userCode": "U001",
        "userName": "张三",
        "email": "<EMAIL>",
        "department": "技术部",
        "permissions": ["SOC_USER"],
        "productPermissions": ["产品A", "产品B"]
    }
}
```

#### 3.3.2 获取用户信息
```
GET /api/v1/auth/userinfo
```

**响应数据：**
```json
{
    "id": 1,
    "userCode": "U001",
    "userName": "张三",
    "email": "<EMAIL>",
    "department": "技术部",
    "permissions": ["SOC_USER"],
    "productPermissions": ["产品A", "产品B"]
}
```

#### 3.3.3 用户登出
```
POST /api/v1/auth/logout
```

### 3.4 任务管理接口

#### 3.4.1 创建任务
```
POST /api/v1/tasks
```

**请求参数：**
```json
{
    "taskName": "string",          // 任务名称（必填）
    "countryMto": "string",        // 国家/MTO（可选）
    "mtoBranch": "string",         // MTO分支（可选）
    "customer": "string",          // 客户（可选）
    "project": "string",           // 项目（可选）
    "dataSource": "GBBS"           // 数据源（必填，默认GBBS）
}
```

**响应数据：**
```json
{
    "id": 1,
    "taskCode": "T202401010001",
    "taskName": "中国电信招标任务",
    "countryMto": "中国",
    "mtoBranch": "北京分公司",
    "customer": "中国电信",
    "project": "5G网络建设项目",
    "dataSource": "GBBS",
    "taskType": 1,
    "creatorId": 1,
    "createdTime": "2024-01-01T12:00:00Z"
}
```

#### 3.4.2 查询任务列表
```
GET /api/v1/tasks
```

**查询参数：**
- `taskCode`: 任务编码（精确查询）
- `taskName`: 任务名称（模糊查询）
- `country`: 国家（精确查询）
- `customer`: 客户（精确查询）
- `project`: 项目（精确查询）
- `current`: 当前页码，默认1
- `pageSize`: 每页大小，默认20，可选10/20/50/100/200

**响应数据：**
```json
{
    "list": [
        {
            "id": 1,
            "taskCode": "T202401010001",
            "taskName": "中国电信招标任务",
            "countryMto": "中国",
            "customer": "中国电信",
            "project": "5G网络建设项目",
            "itemCount": 100,              // 应答条目数
            "answeredCount": 80,           // 已应答条目数
            "answerProgress": "80/100",    // 应答进度
            "totalSatisfaction": "85%",    // 总满足度
            "creatorName": "张三",
            "createdTime": "2024-01-01T12:00:00Z",
            "updatedBy": "李四",
            "updatedTime": "2024-01-01T15:00:00Z",
            "canOperate": true             // 当前用户是否可操作
        }
    ],
    "pagination": {
        "current": 1,
        "pageSize": 20,
        "total": 50,
        "totalPages": 3
    }
}
```

#### 3.4.3 获取任务详情
```
GET /api/v1/tasks/{taskId}
```

**响应数据：**
```json
{
    "id": 1,
    "taskCode": "T202401010001",
    "taskName": "中国电信招标任务",
    "countryMto": "中国",
    "mtoBranch": "北京分公司",
    "customer": "中国电信",
    "project": "5G网络建设项目",
    "dataSource": "GBBS",
    "taskType": 1,
    "creatorId": 1,
    "creatorName": "张三",
    "status": 1,
    "createdTime": "2024-01-01T12:00:00Z",
    "updatedTime": "2024-01-01T15:00:00Z",
    "canEdit": true,     // 是否可编辑
    "canDelete": true    // 是否可删除
}
```

#### 3.4.4 更新任务
```
PUT /api/v1/tasks/{taskId}
```

**请求参数：**
```json
{
    "taskName": "string",
    "countryMto": "string", 
    "mtoBranch": "string",
    "customer": "string",
    "project": "string",
    "dataSource": "string"
}
```

#### 3.4.5 复制任务
```
POST /api/v1/tasks/{taskId}/copy
```

**请求参数：**
```json
{
    "taskName": "string",        // 新任务名称
    "copyResponses": true        // 是否复制条目应答结果
}
```

#### 3.4.6 删除任务
```
DELETE /api/v1/tasks/{taskId}
```

### 3.5 条目管理接口

#### 3.5.1 查询条目列表
```
GET /api/v1/tasks/{taskId}/items
```

**查询参数：**
- `itemCode`: 条目编号（精确查询）
- `itemDescription`: 条目描述（模糊查询）
- `product`: 产品（精确查询）
- `responseStatus`: 应答状态（精确查询）0-未应答，1-应答中，2-已应答
- `tags`: 标签（精确查询）
- `complianceLevel`: 应答满足度（精确查询）FC/PC/NC
- `assignedTo`: 指派给（精确查询）
- `responseMethod`: 应答方式（精确查询）AI/手工
- `responseSource`: 应答来源（精确查询）
- `current`: 当前页码，默认1
- `pageSize`: 每页大小，默认20

**响应数据：**
```json
{
    "list": [
        {
            "id": 1,
            "itemCode": "1.1",
            "itemDescription": "系统应支持高可用性",
            "supplementInfo": "补充信息",
            "tags": [
                {"id": 1, "name": "高可用", "color": "#1890ff"},
                {"id": 2, "name": "系统要求", "color": "#52c41a"}
            ],
            "assignedTo": 2,
            "assignedToName": "李四",
            "responses": [
                {
                    "id": 1,
                    "product": "产品A",
                    "responseStatus": 2,
                    "complianceLevel": "FC",
                    "responseContent": "系统支持99.9%可用性...",
                    "responseMethod": "AI",
                    "responseSource": "GBBS",
                    "sourceIndex": "GBBS-001-5.1",
                    "matchScore": 95.5,
                    "updatedBy": "李四",
                    "updatedTime": "2024-01-01T15:00:00Z"
                }
            ],
            "remarks": "备注信息",
            "createdBy": "张三",
            "createdTime": "2024-01-01T12:00:00Z"
        }
    ],
    "pagination": {
        "current": 1,
        "pageSize": 20,
        "total": 100,
        "totalPages": 5
    }
}
```

#### 3.5.2 单条录入条目
```
POST /api/v1/tasks/{taskId}/items
```

**请求参数：**
```json
{
    "itemCode": "string",                // 条目编号（必填）
    "itemDescription": "string",         // 条目描述（必填）
    "product": "string",                 // 产品（可选）
    "tags": ["string"],                  // 标签数组（可选）
    "complianceLevel": "FC",             // 满足度（可选）
    "assignedTo": 1,                     // 指派给用户ID（可选，默认创建人）
    "responseContent": "string",         // 应答说明（可选）
    "supplementInfo": "string",          // 补充信息（可选）
    "autoAnswer": true,                  // 自动应答（必填，默认true）
    "overwriteOnDuplicate": true,        // 重复时覆盖（必填，默认true）
    "remarks": "string"                  // 备注（可选）
}
```

#### 3.5.3 批量导入条目
```
POST /api/v1/tasks/{taskId}/items/import
```

**请求参数：**
- `file`: Excel文件（必填）
- `autoAnswer`: 是否自动应答（可选，默认true）
- `overwriteOnDuplicate`: 重复时是否覆盖（可选，默认true）

**响应数据：**
```json
{
    "importResult": {
        "totalCount": 100,
        "successCount": 95,
        "failCount": 5,
        "duplicateCount": 3,
        "skippedCount": 2,
        "failedItems": [
            {
                "row": 10,
                "itemCode": "1.10", 
                "error": "条目描述不能为空"
            }
        ],
        "duplicateItems": [
            {
                "row": 15,
                "itemCode": "1.15",
                "message": "条目已存在，已跳过"
            }
        ]
    }
}
```

#### 3.5.4 批量操作条目
```
POST /api/v1/tasks/{taskId}/items/batch
```

**请求参数：**
```json
{
    "action": "START_ANSWER|DELETE|ADD_TAGS|REMOVE_TAGS|SET_PRODUCT|ASSIGN",
    "itemIds": [1, 2, 3],              // 条目ID数组，为空表示全部
    "params": {
        "tags": ["标签1", "标签2"],    // 添加/删除标签时使用
        "product": "产品A",             // 设置产品时使用
        "assignedTo": 1                 // 指派时使用
    }
}
```

#### 3.5.5 导出条目
```
GET /api/v1/tasks/{taskId}/items/export
```

**查询参数：**
- `products`: 产品列表，多个用逗号分隔
- `format`: 导出格式，默认excel
- `itemIds`: 指定条目ID列表，多个用逗号分隔

**响应：**
- 文件下载流

#### 3.5.6 获取导入模板
```
GET /api/v1/tasks/{taskId}/items/template
```

**响应：**
- Excel模板文件下载

### 3.6 条目应答接口

#### 3.6.1 获取条目应答详情
```
GET /api/v1/items/{itemId}/responses/{responseId}
```

**响应数据：**
```json
{
    "id": 1,
    "itemId": 1,
    "itemCode": "1.1",
    "itemDescription": "系统应支持高可用性",
    "supplementInfo": "补充信息",
    "product": "产品A",
    "responseStatus": 2,
    "complianceLevel": "FC",
    "responseContent": "系统支持99.9%可用性...",
    "responseMethod": "AI",
    "responseSource": "GBBS",
    "sourceIndex": "GBBS-001-5.1",
    "matchScore": 95.5,
    "version": 1,
    "assignedTo": 2,
    "assignedToName": "李四",
    "createdBy": "张三",
    "createdTime": "2024-01-01T12:00:00Z",
    "updatedBy": "李四",
    "updatedTime": "2024-01-01T15:00:00Z",
    "taskInfo": {
        "id": 1,
        "taskName": "中国电信招标任务",
        "countryMto": "中国",
        "mtoBranch": "北京分公司",
        "customer": "中国电信",
        "project": "5G网络建设项目",
        "dataSource": "GBBS"
    }
}
```

#### 3.6.2 更新条目应答
```
PUT /api/v1/items/{itemId}/responses/{responseId}
```

**请求参数：**
```json
{
    "supplementInfo": "string",         // 补充信息（可选）
    "complianceLevel": "FC",            // 满足度（可选）
    "responseContent": "string",        // 应答说明（可选）
    "sourceIndex": "string",            // 索引（可选）
    "remarks": "string"                 // 备注（可选）
}
```

#### 3.6.3 AI应答条目
```
POST /api/v1/items/{itemId}/ai-answer
```

**请求参数：**
```json
{
    "product": "string",               // 产品（必填）
    "supplementInfo": "string"         // 补充信息（可选）
}
```

**响应数据：**
```json
{
    "responseId": 1,
    "status": "processing",            // processing-处理中, completed-完成, failed-失败
    "message": "AI应答处理中"
}
```

#### 3.6.4 获取AI匹配结果
```
GET /api/v1/items/{itemId}/ai-matches
```

**查询参数：**
- `product`: 产品筛选
- `complianceLevel`: 满足度筛选（FC/PC/NC）
- `matchScore`: 匹配度筛选（≥90%，≥80%，≥70%）
- `dataSource`: 数据源筛选
- `current`: 当前页码，默认1
- `pageSize`: 每页大小，默认10

**响应数据：**
```json
{
    "matches": [
        {
            "id": 1,
            "dataSource": "GBBS",
            "sourceItemId": "GBBS-001",
            "sourceDescription": "系统高可用性要求",
            "matchScore": 95.5,
            "countryMatch": true,
            "branchMatch": true,
            "customerMatch": false,
            "matchStars": "★★☆",          // 匹配星级显示
            "complianceLevel": "FC",
            "responseContent": "系统支持99.9%可用性...",
            "sourceIndex": "GBBS-001-5.1",
            "isSelected": false,
            "matchRank": 1
        }
    ],
    "summary": {
        "GBBS": {
            "totalCount": 10,
            "fcCount": 6,
            "pcCount": 3,
            "ncCount": 1
        }
    },
    "pagination": {
        "current": 1,
        "pageSize": 10,
        "total": 10,
        "totalPages": 1
    }
}
```

#### 3.6.5 应用AI匹配结果
```
POST /api/v1/items/{itemId}/apply-match
```

**请求参数：**
```json
{
    "matchId": 1,                      // AI匹配结果ID
    "product": "string"                // 产品
}
```

#### 3.6.6 AI润色应答内容
```
POST /api/v1/items/{itemId}/responses/{responseId}/polish
```

**请求参数：**
```json
{
    "content": "string",               // 原始内容
    "style": "formal"                  // 润色风格：formal-正式, concise-简洁
}
```

**响应数据：**
```json
{
    "originalContent": "原始内容",
    "polishedContent": "润色后内容",
    "suggestions": ["建议1", "建议2"]
}
```

#### 3.6.7 AI翻译应答内容
```
POST /api/v1/items/{itemId}/responses/{responseId}/translate
```

**请求参数：**
```json
{
    "content": "string",               // 待翻译内容
    "targetLanguage": "en"             // 目标语言：en-英语, zh-中文
}
```

**响应数据：**
```json
{
    "originalContent": "原始内容",
    "translatedContent": "翻译后内容",
    "sourceLanguage": "zh",
    "targetLanguage": "en"
}
```

### 3.7 数据分析接口

#### 3.7.1 获取任务数据分析
```
GET /api/v1/tasks/{taskId}/analytics
```

**查询参数：**
- `assignedTo`: 指派给用户ID（可选，任务创建人可查看所有用户数据）

**响应数据：**
```json
{
    "overview": {
        "totalItems": 100,             // 总条目数
        "answeredItems": 80,           // 已应答数
        "unansweredItems": 15,         // 未应答数
        "processingItems": 5,          // 应答中数
        "answerRate": "80%",           // 应答完成率
        "fcCount": 50,                 // FC数量
        "pcCount": 25,                 // PC数量
        "ncCount": 5,                  // NC数量
        "satisfaction": "85%"          // 满足度 (FC+PC)/总数
    },
    "productAnalytics": [
        {
            "product": "产品A",
            "totalItems": 50,
            "answeredItems": 40,
            "fcCount": 25,
            "pcCount": 12,
            "ncCount": 3,
            "satisfaction": "88%"
        }
    ],
    "assigneeAnalytics": [             // 仅任务创建人可见
        {
            "assigneeId": 2,
            "assigneeName": "李四",
            "totalItems": 30,
            "answeredItems": 25,
            "fcCount": 15,
            "pcCount": 8,
            "ncCount": 2,
            "satisfaction": "82%"
        }
    ]
}
```

### 3.8 快捷应答接口

#### 3.8.1 快捷应答
```
POST /api/v1/quick-answer
```

**请求参数：**
```json
{
    "dataSource": "GBBS",             // 数据源（必填）
    "product": "string",              // 产品（必填）
    "countryMto": "string",           // 国家/MTO（可选）
    "mtoBranch": "string",            // MTO分支（可选）
    "customer": "string",             // 客户（可选）
    "itemDescription": "string",      // 条目描述（必填）
    "supplementInfo": "string"        // 补充信息（可选）
}
```

**响应数据：**
```json
{
    "taskId": 1,                      // 自动创建的个人任务ID
    "itemId": 1,                      // 条目ID
    "responseId": 1,                  // 应答ID
    "redirectUrl": "/tasks/1/items",  // 跳转URL
    "status": "processing"            // 处理状态
}
```

### 3.9 系统配置接口

#### 3.9.1 获取数据源列表
```
GET /api/v1/config/data-sources
```

**响应数据：**
```json
{
    "dataSources": [
        {
            "code": "GBBS",
            "name": "GBBS系统",
            "type": "GBBS",
            "status": 1
        },
        {
            "code": "DOC_LIB",
            "name": "文档库",
            "type": "文档库",
            "status": 1
        }
    ]
}
```

#### 3.9.2 获取产品目录树
```
GET /api/v1/config/products
```

**查询参数：**
- `category`: 产品分类（SOC标准库、SOC积累库、产品目录树）
- `parentId`: 父级产品ID
- `level`: 产品层级

**响应数据：**
```json
{
    "products": [
        {
            "id": 1,
            "productCode": "PROD_A",
            "productName": "产品A",
            "parentId": null,
            "level": 1,
            "category": "产品目录树",
            "hasChildren": true,
            "children": [
                {
                    "id": 2,
                    "productCode": "PROD_A_1",
                    "productName": "产品A子模块1",
                    "parentId": 1,
                    "level": 2,
                    "category": "产品目录树",
                    "hasChildren": false
                }
            ]
        }
    ]
}
```

#### 3.9.3 获取标签列表
```
GET /api/v1/config/tags
```

**查询参数：**
- `keyword`: 标签名称关键字
- `current`: 当前页码，默认1
- `pageSize`: 每页大小，默认20

**响应数据：**
```json
{
    "list": [
        {
            "id": 1,
            "tagName": "高可用",
            "tagColor": "#1890ff",
            "usageCount": 10,
            "createdBy": "张三",
            "createdTime": "2024-01-01T12:00:00Z"
        }
    ],
    "pagination": {
        "current": 1,
        "pageSize": 20,
        "total": 50,
        "totalPages": 3
    }
}
```

#### 3.9.4 创建标签
```
POST /api/v1/config/tags
```

**请求参数：**
```json
{
    "tagName": "string",              // 标签名称（必填）
    "tagColor": "#1890ff"             // 标签颜色（可选，默认蓝色）
}
```

### 3.10 文件管理接口

#### 3.10.1 上传文件
```
POST /api/v1/files/upload
```

**请求参数：**
- `file`: 文件（必填）
- `relatedType`: 关联类型（可选）TASK_IMPORT-任务导入，RESPONSE_IMAGE-应答图片
- `relatedId`: 关联对象ID（可选）

**响应数据：**
```json
{
    "fileId": 1,
    "fileName": "generated_filename.xlsx",
    "originalName": "条目导入模板.xlsx", 
    "filePath": "/uploads/2024/01/01/xxx.xlsx",
    "fileSize": 1024,
    "fileType": "xlsx",
    "uploadUrl": "https://example.com/uploads/2024/01/01/xxx.xlsx"
}
```

#### 3.10.2 下载文件
```
GET /api/v1/files/{fileId}/download
```

#### 3.10.3 获取文件信息
```
GET /api/v1/files/{fileId}
```

**响应数据：**
```json
{
    "id": 1,
    "fileName": "generated_filename.xlsx",
    "originalName": "条目导入模板.xlsx",
    "fileSize": 1024,
    "fileType": "xlsx",
    "uploadUser": "张三",
    "createdTime": "2024-01-01T12:00:00Z"
}
```

### 3.11 Agent交互接口

#### 3.11.1 Agent对话
```
POST /api/v1/agent/chat
```

**请求参数：**
```json
{
    "message": "string",              // 用户消息（必填）
    "sessionId": "string",            // 会话ID（可选，新会话时为空）
    "context": {                      // 上下文信息（可选）
        "taskId": 1,
        "itemId": 1
    }
}
```

**响应数据：**
```json
{
    "sessionId": "uuid_string",       // 会话ID
    "response": "string",             // Agent回复
    "actions": [                      // 可执行操作
        {
            "type": "CREATE_TASK",
            "label": "创建任务",
            "params": {
                "taskName": "中国电信招标任务1"
            }
        },
        {
            "type": "NAVIGATE",
            "label": "查看任务",
            "params": {
                "url": "/tasks/1"
            }
        }
    ],
    "suggestions": [                  // 建议操作
        "查看任务列表",
        "创建新任务",
        "开始快捷应答"
    ],
    "cards": [                        // 卡片信息
        {
            "type": "TASK_LIST",
            "title": "最近任务",
            "data": [
                {
                    "id": 1,
                    "taskName": "中国电信招标任务",
                    "answerProgress": "80/100"
                }
            ]
        }
    ]
}
```

#### 3.11.2 获取Agent会话历史
```
GET /api/v1/agent/sessions/{sessionId}/history
```

**响应数据：**
```json
{
    "messages": [
        {
            "id": 1,
            "type": "user",           // user-用户消息, agent-AI回复
            "content": "创建中国电信的招标任务",
            "timestamp": "2024-01-01T12:00:00Z"
        },
        {
            "id": 2,
            "type": "agent",
            "content": "好的，我已为您创建了任务...",
            "timestamp": "2024-01-01T12:01:00Z"
        }
    ]
}
```

### 3.12 相似度分析接口

#### 3.12.1 获取条目相似度分析
```
GET /api/v1/items/{itemId}/similarity
```

**查询参数：**
- `threshold`: 相似度阈值，默认0.7
- `type`: 相似度类型，CONTENT-内容相似，TAG-标签相似
- `limit`: 返回数量限制，默认10

**响应数据：**
```json
{
    "similarItems": [
        {
            "itemId": 2,
            "itemCode": "1.2",
            "itemDescription": "系统应具备高可用性保障",
            "similarityScore": 0.95,
            "similarityType": "CONTENT",
            "taskId": 1,
            "taskName": "中国电信招标任务"
        }
    ]
}
```

### 3.13 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | success | 成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 用户未登录或token无效 |
| 403 | Forbidden | 用户无权限访问 |
| 404 | Not Found | 请求的资源不存在 |
| 409 | Conflict | 资源冲突（如重复创建） |
| 422 | Unprocessable Entity | 请求参数验证失败 |
| 429 | Too Many Requests | 请求频率超限 |
| 500 | Internal Server Error | 服务器内部错误 |
| 502 | Bad Gateway | 网关错误 |
| 503 | Service Unavailable | 服务不可用 |

### 3.14 业务错误码定义

| 业务错误码 | 错误信息 | 说明 |
|-----------|----------|------|
| 10001 | 用户权限不足 | 用户无SOC智能应答权限 |
| 10002 | 任务不存在 | 指定的任务不存在 |
| 10003 | 条目不存在 | 指定的条目不存在 |
| 10004 | 条目重复 | 条目编码在任务中已存在 |
| 10005 | 文件格式错误 | 上传的文件格式不正确 |
| 10006 | 文件内容错误 | 文件内容格式或数据错误 |
| 10007 | AI应答失败 | AI服务调用失败 |
| 10008 | 数据源不可用 | 指定的数据源服务不可用 |
| 10009 | 产品权限不足 | 用户无权限访问指定产品 |
| 10010 | 条目状态错误 | 条目当前状态不允许此操作 |

## 4. 接口安全与规范

### 4.1 认证机制
- 使用JWT Token进行用户认证
- Token有效期为24小时，支持刷新
- 请求头格式：`Authorization: Bearer <token>`

### 4.2 权限控制
- 基于用户权限进行接口访问控制
- 任务创建人和条目指派人拥有相应操作权限
- 产品权限控制用户可访问的产品范围

### 4.3 参数验证
- 严格的参数类型和格式校验
- SQL注入防护
- XSS攻击防护
- 文件上传安全检查

### 4.4 频率限制
- 用户级别：100请求/分钟
- IP级别：1000请求/分钟
- AI应答接口：10请求/分钟

### 4.5 日志记录
- 完整的接口调用日志
- 敏感操作审计日志
- 错误日志记录和告警

### 4.6 接口版本控制
- 使用URL路径版本控制：`/api/v1/`
- 向后兼容原则
- 废弃接口提前通知机制

## 5. 性能优化建议

### 5.1 数据库优化
- 合理设计索引，特别是查询频繁的字段
- 大表分区策略（如操作日志按时间分区）
- 读写分离，查询操作使用从库
- 慢查询监控和优化

### 5.2 缓存策略
- Redis缓存热点数据（用户信息、产品目录、标签等）
- 分页查询结果缓存
- AI匹配结果缓存
- 缓存过期和更新策略

### 5.3 接口优化
- 批量操作接口减少网络请求
- 分页查询避免大数据量返回
- 异步处理耗时操作（AI应答、文件导入）
- 接口响应时间监控

### 5.4 文件处理优化
- 大文件分片上传
- 文件存储使用OSS等云存储
- 图片压缩和CDN加速
- 导出文件异步生成

## 6. 部署架构建议

### 6.1 应用架构
- 前端：Vue.js/React + Ant Design
- 后端：Spring Boot + MyBatis
- 数据库：MySQL 8.0+
- 缓存：Redis 6.0+
- 搜索：Elasticsearch（可选）

### 6.2 部署环境
- 容器化部署（Docker + Kubernetes）
- 微服务架构（可选）
- 负载均衡和高可用
- 监控告警系统

### 6.3 安全防护
- HTTPS协议
- API网关限流
- WAF防护
- 数据加密存储

## 7. 总结

本设计文档基于SOC智能应答系统的需求分析，提供了完整的数据库表结构设计和RESTful API接口设计。设计考虑了系统的可扩展性、安全性和性能要求，为系统开发提供了详细的技术规范。

主要特性：
- 完整的用户权限管理体系
- 灵活的任务和条目管理机制  
- 智能的AI匹配和应答功能
- 丰富的数据分析和统计功能
- 人性化的Agent交互体验
- 完善的文件管理和操作日志

该设计支持系统的核心业务流程，能够满足标书应答自动化的业务需求，显著提升工作效率和应答质量。