# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个SOC智能应答系统项目，旨在通过AI技术自动化标书应答流程，提高应答效率和质量。系统支持多数据源（GBBS、文档库、项目文档、历史SOC文档）的智能匹配和应答生成。

### 核心术语
- **条目(Item)**：从标书中梳理出来的要求/问题
- **应答(Response)**：对条目的回答，通常以产品维度对条目进行应答  
- **SOC**：Statement of Compliance，符合性声明
- **FC**：Full Compliance 完全满足
- **PC**：Partially Compliance 部分满足
- **NC**：Not Compliance 不满足

## 项目结构

```
├── SOC应答系统-表设计与接口设计.md    # 完整的表设计和接口设计文档
├── SOC应答系统_CC.md                  # 详细的需求文档
├── 需求/                              # 需求相关文档
│   └── 需求文档.md
├── 设计文档/                          # 设计相关文档
│   ├── 接口设计.md                    # 接口设计文档
│   ├── 数据库设计.md                  # 数据库设计文档
│   └── 设计总结.md                    # 设计总结
├── 原型/                              # 原型和演示文件
│   └── soc-demo.html                  # 系统原型演示页面
└── 实验/                              # 实验和测试相关
```

## 系统架构

### 技术栈
- **前端**: React + TypeScript + Ant Design
- **后端**: Spring Boot + MyBatis Plus
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **消息队列**: RabbitMQ (用于AI应答异步处理)
- **文件存储**: 本地存储/OSS
- **AI服务**: 集成GBBS系统和大模型服务

### 核心模块
1. **用户管理**: 用户认证、权限控制、产品权限管理
2. **任务管理**: 任务创建、编辑、复制、删除
3. **条目管理**: 条目录入、批量导入、列表查询、批量操作
4. **AI应答**: 智能匹配、自动应答、匹配度分析
5. **人工应答**: 应答编辑、AI润色、翻译功能
6. **数据分析**: 进度统计、满足度分析、产品维度分析
7. **快捷应答**: 单条目快速应答功能
8. **Agent交互**: 智能对话、工具调用

## 数据库设计要点

### 核心表结构
- **用户相关**: `soc_users`, `soc_user_permissions`
- **任务管理**: `soc_tasks`
- **条目管理**: `soc_items`, `soc_item_responses`
- **AI匹配**: `soc_ai_matches`
- **标签管理**: `soc_tags`, `soc_item_tags`
- **系统配置**: `soc_data_sources`, `soc_products`
- **文件管理**: `soc_files`
- **操作日志**: `soc_operation_logs`

### 关键设计原则
- 数据一致性通过外键约束保证
- 合理设计索引支持高频查询
- 预留扩展字段支持业务发展
- 敏感数据加密和完整审计日志

## API接口设计

### 接口规范
- **RESTful风格**: 统一的URL设计和HTTP方法使用
- **基础路径**: `/api/v1/`
- **认证方式**: JWT Token认证
- **响应格式**: 统一的JSON格式
- **分页支持**: 标准分页参数 (current, pageSize)

### 主要接口模块
1. **用户认证**: `/api/v1/auth/`
2. **任务管理**: `/api/v1/tasks/`
3. **条目管理**: `/api/v1/tasks/{taskId}/items/`
4. **条目应答**: `/api/v1/items/{itemId}/responses/`
5. **数据分析**: `/api/v1/tasks/{taskId}/analytics`
6. **快捷应答**: `/api/v1/quick-answer`
7. **系统配置**: `/api/v1/config/`
8. **文件管理**: `/api/v1/files/`
9. **Agent交互**: `/api/v1/agent/`

## 核心业务流程

### 任务创建流程
```
用户填写任务信息 → 上传条目文件(可选) → 创建任务记录 → 
解析条目文件 → 批量创建条目 → 触发自动应答(可选)
```

### AI应答流程  
```
触发AI应答 → 调用匹配服务 → 获取GBBS数据 → 
计算匹配度 → 生成应答结果 → 更新条目状态
```

### 人工应答流程
```
查看条目详情 → 查看AI匹配结果 → 编辑应答内容 → 
选择满足度 → 保存应答结果 → 更新条目状态
```

## 权限控制

### 用户角色
- **SOC智能应答-普通用户**: 基础的应答操作权限

### 操作权限
- **任务创建人**: 可查看和操作全部条目
- **条目指派人**: 可查看和操作指派给自己的条目
- **产品权限**: 用户只能访问已申请权限的产品

## 关键功能特性

### 智能匹配
- 基于产品、国家、客户等维度的智能匹配
- 多数据源支持（GBBS、文档库、项目文档等）
- 匹配度评分和排序
- 相似度分析和推荐

### 批量操作
- 批量导入条目（Excel支持）
- 批量应答处理
- 批量标签管理
- 批量指派和产品设置

### AI增强功能
- AI应答内容润色
- 多语言翻译支持
- 智能相似度分析
- Agent对话交互

## 开发注意事项

### 数据安全
- 所有敏感操作需要记录操作日志
- 用户权限严格校验
- 文件上传安全检查
- SQL注入防护

### 性能优化
- 数据库查询优化，合理使用索引
- 大文件异步处理
- 缓存策略应用
- 分页查询优化

### 错误处理
- 统一的错误码和错误信息
- 完善的异常处理机制
- 用户友好的错误提示
- 详细的错误日志记录

## 部署和运维

### 生产环境要求
- Java 8+
- MySQL 8.0+
- Redis 6.0+
- 充足的存储空间用于文件上传

### 监控要点
- 应用性能监控
- 数据库性能监控
- AI服务调用监控
- 文件上传处理监控

## 文档参考

主要技术文档位于项目根目录：
- `SOC应答系统-表设计与接口设计.md`: 完整的数据库表结构和API接口设计
- `SOC应答系统_CC.md`: 详细的业务需求和功能规格说明
- `设计文档/设计总结.md`: 系统架构和技术方案总结
- `原型/soc-demo.html`: 系统UI原型演示

在开发过程中，请参考这些文档了解具体的业务逻辑、数据结构和接口规范。