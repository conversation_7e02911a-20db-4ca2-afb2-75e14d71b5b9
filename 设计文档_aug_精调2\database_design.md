# SOC智能应答系统 - 数据库设计文档

## 目录
1. [概述](#概述)
2. [业务实体分析](#业务实体分析)
3. [数据库表设计](#数据库表设计)
4. [表关系设计](#表关系设计)
5. [索引设计](#索引设计)
6. [建表SQL语句](#建表sql语句)
7. [数据安全性考虑](#数据安全性考虑)

## 概述

本文档基于SOC智能应答系统的需求文档，设计了完整的数据库表结构。系统主要用于标书应答的智能化处理，包括任务管理、条目应答、数据分析等核心功能。

### 设计原则
- 遵循第三范式，减少数据冗余
- 支持高并发读写操作
- 考虑数据扩展性和维护性
- 确保数据完整性和一致性
- 支持软删除机制

## 业务实体分析

根据需求文档分析，系统涉及以下核心业务实体：

### 主要实体
1. **用户（User）** - 系统用户信息
2. **任务（Task）** - SOC应答任务
3. **条目（Item）** - 应答条目
4. **应答（Response）** - 条目应答结果
5. **标签（Tag）** - 条目标签
6. **数据源（DataSource）** - 应答数据来源
7. **匹配记录（MatchRecord）** - AI匹配详情
8. **操作日志（OperationLog）** - 系统操作记录

### 关联关系
- 用户与任务：一对多（一个用户可创建多个任务）
- 任务与条目：一对多（一个任务包含多个条目）
- 条目与应答：一对多（一个条目可有多个产品维度的应答）
- 条目与标签：多对多（一个条目可有多个标签）
- 应答与匹配记录：一对多（一个应答可有多个匹配记录）

## 数据库表设计

### 1. 用户表（soc_user）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 用户ID |
| user_code | VARCHAR | 50 | NOT NULL, UNIQUE | - | 用户工号 |
| user_name | VARCHAR | 100 | NOT NULL | - | 用户姓名 |
| email | VARCHAR | 200 | - | - | 邮箱地址 |
| department | VARCHAR | 200 | - | - | 部门 |
| role | VARCHAR | 50 | NOT NULL | 'USER' | 角色：ADMIN/USER |
| status | TINYINT | - | NOT NULL | 1 | 状态：1-启用，0-禁用 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| is_deleted | TINYINT | - | NOT NULL | 0 | 是否删除：1-已删除，0-未删除 |

### 2. 任务表（soc_task）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 任务ID |
| task_code | VARCHAR | 100 | NOT NULL, UNIQUE | - | 任务编码 |
| task_name | VARCHAR | 200 | NOT NULL | - | 任务名称 |
| country | VARCHAR | 100 | - | - | 国家/MTO |
| mto_branch | VARCHAR | 100 | - | - | MTO分支 |
| customer | VARCHAR | 200 | - | - | 客户 |
| project | VARCHAR | 200 | - | - | 项目 |
| data_source | VARCHAR | 50 | NOT NULL | 'GBBS' | 数据源：GBBS/文档库/项目文档/历史SOC文档 |
| creator_id | BIGINT | - | NOT NULL | - | 创建人ID |
| creator_name | VARCHAR | 100 | - | - | 创建人姓名 |
| total_items | INT | - | NOT NULL | 0 | 总条目数 |
| answered_items | INT | - | NOT NULL | 0 | 已应答条目数 |
| fc_count | INT | - | NOT NULL | 0 | FC数量 |
| pc_count | INT | - | NOT NULL | 0 | PC数量 |
| nc_count | INT | - | NOT NULL | 0 | NC数量 |
| satisfaction_rate | DECIMAL | 5,2 | NOT NULL | 0.00 | 总满足度（百分比） |
| status | VARCHAR | 20 | NOT NULL | 'ACTIVE' | 任务状态：ACTIVE/COMPLETED/ARCHIVED |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| is_deleted | TINYINT | - | NOT NULL | 0 | 是否删除：1-已删除，0-未删除 |

### 3. 条目表（soc_item）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 条目ID |
| task_id | BIGINT | - | NOT NULL | - | 任务ID |
| item_code | VARCHAR | 100 | NOT NULL | - | 条目编号 |
| item_description | TEXT | - | NOT NULL | - | 条目描述 |
| supplement_info | TEXT | - | - | - | 补充信息 |
| assignee_id | BIGINT | - | - | - | 指派人ID |
| assignee_name | VARCHAR | 100 | - | - | 指派人姓名 |
| status | VARCHAR | 20 | NOT NULL | 'PENDING' | 状态：PENDING/PROCESSING/COMPLETED |
| auto_response | TINYINT | - | NOT NULL | 1 | 是否自动应答：1-是，0-否 |
| overwrite_on_duplicate | TINYINT | - | NOT NULL | 1 | 重复时是否覆盖：1-是，0-否 |
| remark | TEXT | - | - | - | 备注 |
| creator_id | BIGINT | - | NOT NULL | - | 创建人ID |
| creator_name | VARCHAR | 100 | - | - | 创建人姓名 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| last_updated_by | BIGINT | - | - | - | 最后更新人ID |
| last_updated_name | VARCHAR | 100 | - | - | 最后更新人姓名 |
| is_deleted | TINYINT | - | NOT NULL | 0 | 是否删除：1-已删除，0-未删除 |

### 4. 应答表（soc_response）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 应答ID |
| item_id | BIGINT | - | NOT NULL | - | 条目ID |
| product | VARCHAR | 200 | NOT NULL | - | 产品 |
| satisfaction | VARCHAR | 10 | - | - | 满足度：FC/PC/NC |
| response_content | LONGTEXT | - | - | - | 应答说明（富文本） |
| response_method | VARCHAR | 20 | - | - | 应答方式：AI/MANUAL |
| data_source | VARCHAR | 50 | - | - | 应答来源 |
| source_index | VARCHAR | 500 | - | - | 索引信息 |
| match_score | DECIMAL | 5,2 | - | - | 匹配度 |
| version | INT | - | NOT NULL | 1 | 版本号 |
| status | VARCHAR | 20 | NOT NULL | 'DRAFT' | 状态：DRAFT/PUBLISHED |
| creator_id | BIGINT | - | NOT NULL | - | 创建人ID |
| creator_name | VARCHAR | 100 | - | - | 创建人姓名 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |
| last_updated_by | BIGINT | - | - | - | 最后更新人ID |
| last_updated_name | VARCHAR | 100 | - | - | 最后更新人姓名 |
| is_deleted | TINYINT | - | NOT NULL | 0 | 是否删除：1-已删除，0-未删除 |

### 5. 标签表（soc_tag）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 标签ID |
| tag_name | VARCHAR | 100 | NOT NULL, UNIQUE | - | 标签名称 |
| tag_color | VARCHAR | 20 | - | '#1890ff' | 标签颜色 |
| usage_count | INT | - | NOT NULL | 0 | 使用次数 |
| creator_id | BIGINT | - | NOT NULL | - | 创建人ID |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| is_deleted | TINYINT | - | NOT NULL | 0 | 是否删除：1-已删除，0-未删除 |

### 6. 条目标签关联表（soc_item_tag）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 关联ID |
| item_id | BIGINT | - | NOT NULL | - | 条目ID |
| tag_id | BIGINT | - | NOT NULL | - | 标签ID |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

### 7. 匹配记录表（soc_match_record）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 匹配记录ID |
| response_id | BIGINT | - | NOT NULL | - | 应答ID |
| data_source | VARCHAR | 50 | NOT NULL | - | 数据源 |
| source_item_code | VARCHAR | 100 | - | - | 源条目编号 |
| source_description | TEXT | - | - | - | 源条目描述 |
| source_satisfaction | VARCHAR | 10 | - | - | 源满足度 |
| source_content | LONGTEXT | - | - | - | 源应答内容 |
| source_index | VARCHAR | 500 | - | - | 源索引 |
| match_score | DECIMAL | 5,2 | NOT NULL | 0.00 | 匹配度 |
| country_match | TINYINT | - | NOT NULL | 0 | 国家匹配：1-匹配，0-不匹配 |
| branch_match | TINYINT | - | NOT NULL | 0 | 分支匹配：1-匹配，0-不匹配 |
| customer_match | TINYINT | - | NOT NULL | 0 | 客户匹配：1-匹配，0-不匹配 |
| is_applied | TINYINT | - | NOT NULL | 0 | 是否已应用：1-已应用，0-未应用 |
| created_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

### 8. 操作日志表（soc_operation_log）

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 注释 |
|--------|----------|------|------|--------|------|
| id | BIGINT | - | PK, AUTO_INCREMENT | - | 日志ID |
| user_id | BIGINT | - | NOT NULL | - | 操作用户ID |
| user_name | VARCHAR | 100 | - | - | 操作用户姓名 |
| operation_type | VARCHAR | 50 | NOT NULL | - | 操作类型 |
| operation_desc | VARCHAR | 500 | - | - | 操作描述 |
| target_type | VARCHAR | 50 | - | - | 目标类型：TASK/ITEM/RESPONSE |
| target_id | BIGINT | - | - | - | 目标ID |
| ip_address | VARCHAR | 50 | - | - | IP地址 |
| user_agent | VARCHAR | 500 | - | - | 用户代理 |
| operation_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 操作时间 |

## 表关系设计

### ER图关系说明

```
用户(soc_user) 1:N 任务(soc_task)
任务(soc_task) 1:N 条目(soc_item)
条目(soc_item) 1:N 应答(soc_response)
条目(soc_item) N:M 标签(soc_tag) [通过soc_item_tag关联]
应答(soc_response) 1:N 匹配记录(soc_match_record)
用户(soc_user) 1:N 操作日志(soc_operation_log)
```

### 外键约束

- `soc_task.creator_id` → `soc_user.id`
- `soc_item.task_id` → `soc_task.id`
- `soc_item.assignee_id` → `soc_user.id`
- `soc_response.item_id` → `soc_item.id`
- `soc_item_tag.item_id` → `soc_item.id`
- `soc_item_tag.tag_id` → `soc_tag.id`
- `soc_match_record.response_id` → `soc_response.id`
- `soc_operation_log.user_id` → `soc_user.id`

## 索引设计

### 主要索引策略

1. **任务表索引**
   - `idx_task_creator` (creator_id)
   - `idx_task_status` (status, is_deleted)
   - `idx_task_name` (task_name)
   - `idx_task_created_time` (created_time)

2. **条目表索引**
   - `idx_item_task` (task_id, is_deleted)
   - `idx_item_assignee` (assignee_id)
   - `idx_item_status` (status)
   - `idx_item_code` (item_code, task_id)

3. **应答表索引**
   - `idx_response_item` (item_id, is_deleted)
   - `idx_response_product` (product)
   - `idx_response_satisfaction` (satisfaction)
   - `idx_response_method` (response_method)

4. **标签关联表索引**
   - `idx_item_tag_item` (item_id)
   - `idx_item_tag_tag` (tag_id)
   - `uk_item_tag` (item_id, tag_id) UNIQUE

5. **匹配记录表索引**
   - `idx_match_response` (response_id)
   - `idx_match_score` (match_score DESC)
   - `idx_match_source` (data_source)

6. **操作日志表索引**
   - `idx_log_user` (user_id)
   - `idx_log_time` (operation_time)
   - `idx_log_target` (target_type, target_id)

## 建表SQL语句

### 创建数据库
```sql
CREATE DATABASE soc_intelligent_response 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE soc_intelligent_response;
```

### 1. 用户表
```sql
CREATE TABLE soc_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    user_code VARCHAR(50) NOT NULL UNIQUE COMMENT '用户工号',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    email VARCHAR(200) COMMENT '邮箱地址',
    department VARCHAR(200) COMMENT '部门',
    role VARCHAR(50) NOT NULL DEFAULT 'USER' COMMENT '角色：ADMIN/USER',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：1-已删除，0-未删除',
    
    INDEX idx_user_code (user_code),
    INDEX idx_user_status (status, is_deleted),
    INDEX idx_user_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 2. 任务表
```sql
CREATE TABLE soc_task (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '任务ID',
    task_code VARCHAR(100) NOT NULL UNIQUE COMMENT '任务编码',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country VARCHAR(100) COMMENT '国家/MTO',
    mto_branch VARCHAR(100) COMMENT 'MTO分支',
    customer VARCHAR(200) COMMENT '客户',
    project VARCHAR(200) COMMENT '项目',
    data_source VARCHAR(50) NOT NULL DEFAULT 'GBBS' COMMENT '数据源：GBBS/文档库/项目文档/历史SOC文档',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    creator_name VARCHAR(100) COMMENT '创建人姓名',
    total_items INT NOT NULL DEFAULT 0 COMMENT '总条目数',
    answered_items INT NOT NULL DEFAULT 0 COMMENT '已应答条目数',
    fc_count INT NOT NULL DEFAULT 0 COMMENT 'FC数量',
    pc_count INT NOT NULL DEFAULT 0 COMMENT 'PC数量',
    nc_count INT NOT NULL DEFAULT 0 COMMENT 'NC数量',
    satisfaction_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '总满足度（百分比）',
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '任务状态：ACTIVE/COMPLETED/ARCHIVED',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：1-已删除，0-未删除',
    
    INDEX idx_task_creator (creator_id),
    INDEX idx_task_status (status, is_deleted),
    INDEX idx_task_name (task_name),
    INDEX idx_task_created_time (created_time),
    INDEX idx_task_code (task_code),
    
    FOREIGN KEY (creator_id) REFERENCES soc_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';
```

### 3. 条目表
```sql
CREATE TABLE soc_item (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    item_description TEXT NOT NULL COMMENT '条目描述',
    supplement_info TEXT COMMENT '补充信息',
    assignee_id BIGINT COMMENT '指派人ID',
    assignee_name VARCHAR(100) COMMENT '指派人姓名',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING/PROCESSING/COMPLETED',
    auto_response TINYINT NOT NULL DEFAULT 1 COMMENT '是否自动应答：1-是，0-否',
    overwrite_on_duplicate TINYINT NOT NULL DEFAULT 1 COMMENT '重复时是否覆盖：1-是，0-否',
    remark TEXT COMMENT '备注',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    creator_name VARCHAR(100) COMMENT '创建人姓名',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_updated_by BIGINT COMMENT '最后更新人ID',
    last_updated_name VARCHAR(100) COMMENT '最后更新人姓名',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：1-已删除，0-未删除',

    INDEX idx_item_task (task_id, is_deleted),
    INDEX idx_item_assignee (assignee_id),
    INDEX idx_item_status (status),
    INDEX idx_item_code (item_code, task_id),
    INDEX idx_item_created_time (created_time),

    FOREIGN KEY (task_id) REFERENCES soc_task(id),
    FOREIGN KEY (assignee_id) REFERENCES soc_user(id),
    FOREIGN KEY (creator_id) REFERENCES soc_user(id),
    FOREIGN KEY (last_updated_by) REFERENCES soc_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='条目表';
```

### 4. 应答表
```sql
CREATE TABLE soc_response (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '应答ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    product VARCHAR(200) NOT NULL COMMENT '产品',
    satisfaction VARCHAR(10) COMMENT '满足度：FC/PC/NC',
    response_content LONGTEXT COMMENT '应答说明（富文本）',
    response_method VARCHAR(20) COMMENT '应答方式：AI/MANUAL',
    data_source VARCHAR(50) COMMENT '应答来源',
    source_index VARCHAR(500) COMMENT '索引信息',
    match_score DECIMAL(5,2) COMMENT '匹配度',
    version INT NOT NULL DEFAULT 1 COMMENT '版本号',
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态：DRAFT/PUBLISHED',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    creator_name VARCHAR(100) COMMENT '创建人姓名',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_updated_by BIGINT COMMENT '最后更新人ID',
    last_updated_name VARCHAR(100) COMMENT '最后更新人姓名',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：1-已删除，0-未删除',

    INDEX idx_response_item (item_id, is_deleted),
    INDEX idx_response_product (product),
    INDEX idx_response_satisfaction (satisfaction),
    INDEX idx_response_method (response_method),
    INDEX idx_response_created_time (created_time),
    UNIQUE KEY uk_response_item_product (item_id, product, is_deleted),

    FOREIGN KEY (item_id) REFERENCES soc_item(id),
    FOREIGN KEY (creator_id) REFERENCES soc_user(id),
    FOREIGN KEY (last_updated_by) REFERENCES soc_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应答表';
```

### 5. 标签表
```sql
CREATE TABLE soc_tag (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL UNIQUE COMMENT '标签名称',
    tag_color VARCHAR(20) DEFAULT '#1890ff' COMMENT '标签颜色',
    usage_count INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    creator_id BIGINT NOT NULL COMMENT '创建人ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：1-已删除，0-未删除',

    INDEX idx_tag_name (tag_name),
    INDEX idx_tag_usage (usage_count DESC),
    INDEX idx_tag_created_time (created_time),

    FOREIGN KEY (creator_id) REFERENCES soc_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';
```

### 6. 条目标签关联表
```sql
CREATE TABLE soc_item_tag (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_item_tag_item (item_id),
    INDEX idx_item_tag_tag (tag_id),
    UNIQUE KEY uk_item_tag (item_id, tag_id),

    FOREIGN KEY (item_id) REFERENCES soc_item(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES soc_tag(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='条目标签关联表';
```

### 7. 匹配记录表
```sql
CREATE TABLE soc_match_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '匹配记录ID',
    response_id BIGINT NOT NULL COMMENT '应答ID',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源',
    source_item_code VARCHAR(100) COMMENT '源条目编号',
    source_description TEXT COMMENT '源条目描述',
    source_satisfaction VARCHAR(10) COMMENT '源满足度',
    source_content LONGTEXT COMMENT '源应答内容',
    source_index VARCHAR(500) COMMENT '源索引',
    match_score DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '匹配度',
    country_match TINYINT NOT NULL DEFAULT 0 COMMENT '国家匹配：1-匹配，0-不匹配',
    branch_match TINYINT NOT NULL DEFAULT 0 COMMENT '分支匹配：1-匹配，0-不匹配',
    customer_match TINYINT NOT NULL DEFAULT 0 COMMENT '客户匹配：1-匹配，0-不匹配',
    is_applied TINYINT NOT NULL DEFAULT 0 COMMENT '是否已应用：1-已应用，0-未应用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_match_response (response_id),
    INDEX idx_match_score (match_score DESC),
    INDEX idx_match_source (data_source),
    INDEX idx_match_applied (is_applied),
    INDEX idx_match_created_time (created_time),

    FOREIGN KEY (response_id) REFERENCES soc_response(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='匹配记录表';
```

### 8. 操作日志表
```sql
CREATE TABLE soc_operation_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    user_name VARCHAR(100) COMMENT '操作用户姓名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(500) COMMENT '操作描述',
    target_type VARCHAR(50) COMMENT '目标类型：TASK/ITEM/RESPONSE',
    target_id BIGINT COMMENT '目标ID',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    operation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',

    INDEX idx_log_user (user_id),
    INDEX idx_log_time (operation_time),
    INDEX idx_log_target (target_type, target_id),
    INDEX idx_log_operation (operation_type),

    FOREIGN KEY (user_id) REFERENCES soc_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';
```

## 数据安全性考虑

### 1. 数据加密
- 敏感字段（如邮箱）可考虑使用AES加密存储
- 数据库连接使用SSL加密传输
- 定期更新数据库密码

### 2. 访问控制
- 实施最小权限原则
- 不同角色用户使用不同数据库账号
- 定期审计数据库访问日志

### 3. 数据备份
- 每日自动备份数据库
- 异地备份存储
- 定期测试备份恢复流程

### 4. 数据完整性
- 使用外键约束保证引用完整性
- 关键业务数据使用事务保证一致性
- 定期检查数据完整性

### 5. 审计追踪
- 所有关键操作记录到操作日志表
- 保留足够的审计信息用于问题追踪
- 定期清理过期日志数据

### 6. 性能优化建议
- 根据查询模式创建合适的复合索引
- 定期分析慢查询并优化
- 考虑对大表进行分区
- 使用读写分离提高并发性能

### 7. 扩展性考虑
- 预留字段用于未来功能扩展
- 使用软删除机制保证数据可恢复
- 考虑使用分布式架构支持大规模数据

## 总结

本数据库设计文档基于SOC智能应答系统的业务需求，设计了8个核心表，涵盖了用户管理、任务管理、条目应答、标签管理、匹配记录和操作审计等功能。设计遵循了数据库设计的最佳实践，考虑了数据完整性、性能优化、安全性和扩展性等方面的需求。

该设计支持：
- 多用户协作的任务管理
- 灵活的条目应答和版本控制
- 智能匹配和推荐功能
- 完整的操作审计追踪
- 良好的扩展性和维护性
```
