# SOC智能应答系统 - API接口设计文档

## 目录
1. [概述](#概述)
2. [接口设计原则](#接口设计原则)
3. [通用规范](#通用规范)
4. [认证与授权](#认证与授权)
5. [用户管理接口](#用户管理接口)
6. [任务管理接口](#任务管理接口)
7. [条目管理接口](#条目管理接口)
8. [应答管理接口](#应答管理接口)
9. [标签管理接口](#标签管理接口)
10. [数据分析接口](#数据分析接口)
11. [文件管理接口](#文件管理接口)
12. [错误码定义](#错误码定义)

## 概述

本文档定义了SOC智能应答系统的RESTful API接口规范，包括用户管理、任务管理、条目应答、数据分析等核心功能模块的接口设计。

### 技术栈
- 协议：HTTP/HTTPS
- 数据格式：JSON
- 认证方式：JWT Token
- API版本：v1

### 基础URL
```
开发环境：https://dev-api.soc.company.com/api/v1
测试环境：https://test-api.soc.company.com/api/v1
生产环境：https://api.soc.company.com/api/v1
```

## 接口设计原则

### 1. RESTful设计
- 使用标准HTTP方法（GET、POST、PUT、DELETE、PATCH）
- URL设计遵循资源导向原则
- 使用HTTP状态码表示操作结果

### 2. 统一响应格式
- 所有接口返回统一的JSON格式
- 包含状态码、消息和数据字段
- 错误信息提供详细的错误描述

### 3. 安全性
- 所有接口需要身份认证
- 敏感操作需要权限验证
- 输入参数严格验证和过滤

### 4. 性能优化
- 支持分页查询
- 支持字段筛选
- 合理使用缓存机制

## 通用规范

### 请求头规范
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-Request-ID: {unique_request_id}
User-Agent: SOC-Client/1.0
```

### 统一响应格式
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": "2024-01-01T12:00:00Z",
    "requestId": "req_123456789"
}
```

### 分页响应格式
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 100,
            "pages": 5
        }
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "requestId": "req_123456789"
}
```

### HTTP状态码使用
- 200：请求成功
- 201：创建成功
- 204：删除成功
- 400：请求参数错误
- 401：未授权
- 403：权限不足
- 404：资源不存在
- 409：资源冲突
- 422：参数验证失败
- 500：服务器内部错误

## 认证与授权

### 1. 用户登录
**接口地址：** `POST /auth/login`

**请求参数：**
```json
{
    "userCode": "123456",
    "password": "encrypted_password"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "refresh_token_string",
        "expiresIn": 7200,
        "user": {
            "id": 1,
            "userCode": "123456",
            "userName": "张三",
            "email": "<EMAIL>",
            "department": "技术部",
            "role": "USER"
        }
    }
}
```

### 2. 刷新Token
**接口地址：** `POST /auth/refresh`

**请求参数：**
```json
{
    "refreshToken": "refresh_token_string"
}
```

### 3. 用户登出
**接口地址：** `POST /auth/logout`

**请求头：** `Authorization: Bearer {token}`

## 用户管理接口

### 1. 获取当前用户信息
**接口地址：** `GET /users/current`

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "userCode": "123456",
        "userName": "张三",
        "email": "<EMAIL>",
        "department": "技术部",
        "role": "USER",
        "status": 1,
        "createdTime": "2024-01-01T12:00:00Z"
    }
}
```

### 2. 搜索用户
**接口地址：** `GET /users/search`

**查询参数：**
- `keyword`：关键字（可选）
- `department`：部门（可选）
- `page`：页码，默认1
- `size`：每页大小，默认20

**响应示例：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "userCode": "123456",
                "userName": "张三",
                "department": "技术部"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 1,
            "pages": 1
        }
    }
}
```

## 任务管理接口

### 1. 创建任务
**接口地址：** `POST /tasks`

**请求参数：**
```json
{
    "taskName": "中国电信5G项目SOC应答",
    "country": "中国",
    "mtoBranch": "广东分公司",
    "customer": "中国电信",
    "project": "5G核心网建设项目",
    "dataSource": "GBBS",
    "itemFile": {
        "fileName": "条目清单.xlsx",
        "fileUrl": "https://file.company.com/uploads/items.xlsx"
    }
}
```

**字段验证规则：**
- `taskName`：必填，长度1-200字符，不能重复
- `country`：可选，长度1-100字符
- `mtoBranch`：可选，长度1-100字符
- `customer`：可选，长度1-200字符
- `project`：可选，长度1-200字符
- `dataSource`：必填，枚举值：GBBS/文档库/项目文档/历史SOC文档

**响应示例：**
```json
{
    "code": 201,
    "message": "任务创建成功",
    "data": {
        "id": 1,
        "taskCode": "TASK_20240101_001",
        "taskName": "中国电信5G项目SOC应答",
        "country": "中国",
        "mtoBranch": "广东分公司",
        "customer": "中国电信",
        "project": "5G核心网建设项目",
        "dataSource": "GBBS",
        "creatorId": 1,
        "creatorName": "张三",
        "totalItems": 0,
        "answeredItems": 0,
        "satisfactionRate": 0.00,
        "status": "ACTIVE",
        "createdTime": "2024-01-01T12:00:00Z"
    }
}
```

### 2. 查询任务列表
**接口地址：** `GET /tasks`

**查询参数：**
- `taskCode`：任务编码（可选）
- `taskName`：任务名称，支持模糊查询（可选）
- `country`：国家（可选）
- `customer`：客户（可选）
- `project`：项目（可选）
- `status`：任务状态（可选）
- `creatorId`：创建人ID（可选）
- `page`：页码，默认1
- `size`：每页大小，默认20，最大200
- `sortBy`：排序字段，默认createdTime
- `sortOrder`：排序方向，ASC/DESC，默认DESC

**响应示例：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "taskCode": "TASK_20240101_001",
                "taskName": "中国电信5G项目SOC应答",
                "country": "中国",
                "customer": "中国电信",
                "project": "5G核心网建设项目",
                "totalItems": 150,
                "answeredItems": 120,
                "progress": "120/150",
                "satisfactionRate": 85.50,
                "creatorName": "张三",
                "createdTime": "2024-01-01T12:00:00Z",
                "updatedTime": "2024-01-01T15:30:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 1,
            "pages": 1
        }
    }
}
```

### 3. 获取任务详情
**接口地址：** `GET /tasks/{taskId}`

**路径参数：**
- `taskId`：任务ID

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "taskCode": "TASK_20240101_001",
        "taskName": "中国电信5G项目SOC应答",
        "country": "中国",
        "mtoBranch": "广东分公司",
        "customer": "中国电信",
        "project": "5G核心网建设项目",
        "dataSource": "GBBS",
        "creatorId": 1,
        "creatorName": "张三",
        "totalItems": 150,
        "answeredItems": 120,
        "fcCount": 80,
        "pcCount": 30,
        "ncCount": 10,
        "satisfactionRate": 85.50,
        "status": "ACTIVE",
        "createdTime": "2024-01-01T12:00:00Z",
        "updatedTime": "2024-01-01T15:30:00Z"
    }
}
```

### 4. 更新任务
**接口地址：** `PUT /tasks/{taskId}`

**路径参数：**
- `taskId`：任务ID

**请求参数：**
```json
{
    "taskName": "中国电信5G项目SOC应答（更新）",
    "country": "中国",
    "mtoBranch": "广东分公司",
    "customer": "中国电信",
    "project": "5G核心网建设项目",
    "dataSource": "GBBS"
}
```

### 5. 复制任务
**接口地址：** `POST /tasks/{taskId}/copy`

**路径参数：**
- `taskId`：源任务ID

**请求参数：**
```json
{
    "taskName": "中国电信5G项目SOC应答_复制",
    "copyResponses": true
}
```

**字段说明：**
- `taskName`：新任务名称，必填
- `copyResponses`：是否复制应答结果，默认false

### 6. 删除任务
**接口地址：** `DELETE /tasks/{taskId}`

**路径参数：**
- `taskId`：任务ID

**权限要求：** 仅任务创建人可删除

**响应示例：**
```json
{
    "code": 204,
    "message": "删除成功"
}
```

## 条目管理接口

### 1. 添加条目（单条录入）
**接口地址：** `POST /tasks/{taskId}/items`

**路径参数：**
- `taskId`：任务ID

**请求参数：**
```json
{
    "itemCode": "REQ_001",
    "itemDescription": "系统应支持高可用性架构设计",
    "products": ["华为云Stack", "FusionSphere"],
    "tags": ["高可用", "架构"],
    "satisfaction": "FC",
    "assigneeId": 2,
    "responseContent": "我们的系统采用双机热备架构...",
    "supplementInfo": "需要考虑容灾备份",
    "autoResponse": true,
    "overwriteOnDuplicate": true,
    "remark": "重要条目"
}
```

**字段验证规则：**
- `itemCode`：必填，长度1-100字符
- `itemDescription`：必填，长度1-5000字符
- `products`：可选，产品数组
- `tags`：可选，标签数组
- `satisfaction`：可选，枚举值：FC/PC/NC
- `assigneeId`：可选，指派人ID
- `autoResponse`：必填，布尔值
- `overwriteOnDuplicate`：必填，布尔值

**响应示例：**
```json
{
    "code": 201,
    "message": "条目添加成功",
    "data": {
        "id": 1,
        "taskId": 1,
        "itemCode": "REQ_001",
        "itemDescription": "系统应支持高可用性架构设计",
        "assigneeId": 2,
        "assigneeName": "李四",
        "status": "PENDING",
        "createdTime": "2024-01-01T12:00:00Z"
    }
}
```

### 2. 批量导入条目
**接口地址：** `POST /tasks/{taskId}/items/batch`

**路径参数：**
- `taskId`：任务ID

**请求参数：**
```json
{
    "fileUrl": "https://file.company.com/uploads/items.xlsx",
    "autoResponse": true,
    "overwriteOnDuplicate": true
}
```

**响应示例：**
```json
{
    "code": 201,
    "message": "批量导入成功",
    "data": {
        "totalCount": 100,
        "successCount": 95,
        "failureCount": 5,
        "duplicateCount": 3,
        "failureDetails": [
            {
                "row": 10,
                "itemCode": "REQ_010",
                "error": "条目描述不能为空"
            }
        ]
    }
}
```

### 3. 查询条目列表
**接口地址：** `GET /tasks/{taskId}/items`

**路径参数：**
- `taskId`：任务ID

**查询参数：**
- `itemCode`：条目编号，精确查询（可选）
- `itemDescription`：条目描述，模糊查询（可选）
- `product`：产品，精确查询（可选）
- `status`：应答状态，精确查询（可选）
- `tag`：标签，精确查询（可选）
- `satisfaction`：满足度，精确查询（可选）
- `assigneeId`：指派人ID，精确查询（可选）
- `responseMethod`：应答方式，精确查询（可选）
- `dataSource`：应答来源，精确查询（可选）
- `page`：页码，默认1
- `size`：每页大小，默认20
- `sortBy`：排序字段，默认createdTime
- `sortOrder`：排序方向，ASC/DESC，默认DESC

**响应示例：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "itemCode": "REQ_001",
                "itemDescription": "系统应支持高可用性架构设计",
                "products": [
                    {
                        "product": "华为云Stack",
                        "status": "COMPLETED",
                        "satisfaction": "FC",
                        "responseContent": "我们的系统采用双机热备架构...",
                        "responseMethod": "AI",
                        "dataSource": "GBBS",
                        "sourceIndex": "GBBS-HA-001",
                        "lastUpdatedBy": "张三",
                        "lastUpdatedTime": "2024-01-01T15:30:00Z"
                    }
                ],
                "tags": ["高可用", "架构"],
                "assigneeName": "李四",
                "remark": "重要条目",
                "createdTime": "2024-01-01T12:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 150,
            "pages": 8
        }
    }
}
```

### 4. 获取条目详情
**接口地址：** `GET /items/{itemId}`

**路径参数：**
- `itemId`：条目ID

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "taskId": 1,
        "itemCode": "REQ_001",
        "itemDescription": "系统应支持高可用性架构设计",
        "supplementInfo": "需要考虑容灾备份",
        "assigneeId": 2,
        "assigneeName": "李四",
        "status": "COMPLETED",
        "tags": ["高可用", "架构"],
        "responses": [
            {
                "id": 1,
                "product": "华为云Stack",
                "satisfaction": "FC",
                "responseContent": "我们的系统采用双机热备架构...",
                "responseMethod": "AI",
                "dataSource": "GBBS",
                "sourceIndex": "GBBS-HA-001",
                "matchScore": 95.5,
                "version": 1,
                "createdTime": "2024-01-01T12:30:00Z"
            }
        ],
        "remark": "重要条目",
        "createdTime": "2024-01-01T12:00:00Z",
        "updatedTime": "2024-01-01T15:30:00Z"
    }
}
```

### 5. 更新条目
**接口地址：** `PUT /items/{itemId}`

**路径参数：**
- `itemId`：条目ID

**请求参数：**
```json
{
    "itemDescription": "系统应支持高可用性架构设计（更新）",
    "supplementInfo": "需要考虑容灾备份和负载均衡",
    "assigneeId": 3,
    "remark": "重要条目，优先处理"
}
```

### 6. 删除条目
**接口地址：** `DELETE /items/{itemId}`

**路径参数：**
- `itemId`：条目ID

### 7. 批量操作条目
**接口地址：** `POST /tasks/{taskId}/items/batch-operation`

**路径参数：**
- `taskId`：任务ID

**请求参数：**
```json
{
    "operation": "START_RESPONSE",
    "itemIds": [1, 2, 3],
    "params": {
        "assigneeId": 2,
        "tags": ["紧急", "重要"],
        "products": ["华为云Stack"]
    }
}
```

**操作类型：**
- `START_RESPONSE`：开始应答
- `DELETE`：批量删除
- `ADD_TAGS`：批量添加标签
- `REMOVE_TAGS`：批量移除标签
- `SET_PRODUCT`：设置产品
- `ASSIGN_TO`：指派给

## 应答管理接口

### 1. AI应答
**接口地址：** `POST /items/{itemId}/ai-response`

**路径参数：**
- `itemId`：条目ID

**请求参数：**
```json
{
    "product": "华为云Stack",
    "supplementInfo": "补充信息用于AI分析"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "AI应答已启动",
    "data": {
        "responseId": 1,
        "status": "PROCESSING",
        "estimatedTime": 30
    }
}
```

### 2. 手工应答
**接口地址：** `POST /items/{itemId}/manual-response`

**路径参数：**
- `itemId`：条目ID

**请求参数：**
```json
{
    "product": "华为云Stack",
    "satisfaction": "FC",
    "responseContent": "<p>我们的系统采用双机热备架构...</p>",
    "sourceIndex": "技术文档-第5章",
    "remark": "手工编写的应答"
}
```

### 3. 获取应答详情
**接口地址：** `GET /responses/{responseId}`

**路径参数：**
- `responseId`：应答ID

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "itemId": 1,
        "product": "华为云Stack",
        "satisfaction": "FC",
        "responseContent": "<p>我们的系统采用双机热备架构...</p>",
        "responseMethod": "AI",
        "dataSource": "GBBS",
        "sourceIndex": "GBBS-HA-001",
        "matchScore": 95.5,
        "version": 1,
        "status": "PUBLISHED",
        "matchRecords": [
            {
                "id": 1,
                "dataSource": "GBBS",
                "sourceDescription": "高可用架构设计要求",
                "sourceSatisfaction": "FC",
                "sourceContent": "系统应采用双机热备...",
                "matchScore": 95.5,
                "countryMatch": true,
                "branchMatch": true,
                "customerMatch": false
            }
        ],
        "createdTime": "2024-01-01T12:30:00Z",
        "updatedTime": "2024-01-01T15:30:00Z"
    }
}
```

### 4. 更新应答
**接口地址：** `PUT /responses/{responseId}`

**路径参数：**
- `responseId`：应答ID

**请求参数：**
```json
{
    "satisfaction": "PC",
    "responseContent": "<p>更新后的应答内容...</p>",
    "sourceIndex": "更新后的索引",
    "remark": "手工修改"
}
```

### 5. 应用匹配结果
**接口地址：** `POST /responses/{responseId}/apply-match`

**路径参数：**
- `responseId`：应答ID

**请求参数：**
```json
{
    "matchRecordId": 1
}
```

## 标签管理接口

### 1. 获取标签列表
**接口地址：** `GET /tags`

**查询参数：**
- `keyword`：标签名称关键字（可选）
- `page`：页码，默认1
- `size`：每页大小，默认50

**响应示例：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "tagName": "高可用",
                "tagColor": "#1890ff",
                "usageCount": 25,
                "createdTime": "2024-01-01T12:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 50,
            "total": 1,
            "pages": 1
        }
    }
}
```

### 2. 创建标签
**接口地址：** `POST /tags`

**请求参数：**
```json
{
    "tagName": "高可用",
    "tagColor": "#1890ff"
}
```

### 3. 更新标签
**接口地址：** `PUT /tags/{tagId}`

**路径参数：**
- `tagId`：标签ID

### 4. 删除标签
**接口地址：** `DELETE /tags/{tagId}`

**路径参数：**
- `tagId`：标签ID

## 数据分析接口

### 1. 获取任务统计数据
**接口地址：** `GET /tasks/{taskId}/statistics`

**路径参数：**
- `taskId`：任务ID

**查询参数：**
- `assigneeId`：指派人ID，用于筛选特定人员的数据（可选）

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "overview": {
            "totalItems": 150,
            "answeredItems": 120,
            "pendingItems": 20,
            "processingItems": 10,
            "completionRate": 80.0,
            "fcCount": 80,
            "pcCount": 30,
            "ncCount": 10,
            "satisfactionRate": 85.5
        },
        "productStatistics": [
            {
                "product": "华为云Stack",
                "totalItems": 60,
                "answeredItems": 50,
                "fcCount": 35,
                "pcCount": 12,
                "ncCount": 3,
                "satisfactionRate": 88.0
            }
        ],
        "assigneeStatistics": [
            {
                "assigneeId": 2,
                "assigneeName": "李四",
                "totalItems": 30,
                "answeredItems": 25,
                "completionRate": 83.3
            }
        ]
    }
}
```

### 2. 获取应答进度趋势
**接口地址：** `GET /tasks/{taskId}/progress-trend`

**路径参数：**
- `taskId`：任务ID

**查询参数：**
- `days`：统计天数，默认7天

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "trend": [
            {
                "date": "2024-01-01",
                "totalItems": 150,
                "answeredItems": 100,
                "completionRate": 66.7
            }
        ]
    }
}
```

## 文件管理接口

### 1. 文件上传
**接口地址：** `POST /files/upload`

**请求类型：** `multipart/form-data`

**请求参数：**
- `file`：文件对象
- `type`：文件类型，枚举值：ITEM_TEMPLATE/ITEM_IMPORT/RESPONSE_EXPORT

**响应示例：**
```json
{
    "code": 200,
    "message": "上传成功",
    "data": {
        "fileId": "file_123456",
        "fileName": "条目清单.xlsx",
        "fileUrl": "https://file.company.com/uploads/items.xlsx",
        "fileSize": 1024000,
        "uploadTime": "2024-01-01T12:00:00Z"
    }
}
```

### 2. 下载导入模板
**接口地址：** `GET /files/template/item-import`

**响应：** 直接返回Excel文件流

### 3. 导出条目应答
**接口地址：** `POST /tasks/{taskId}/export`

**路径参数：**
- `taskId`：任务ID

**请求参数：**
```json
{
    "products": ["华为云Stack", "FusionSphere"],
    "format": "EXCEL",
    "includeImages": true
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "导出任务已创建",
    "data": {
        "exportId": "export_123456",
        "status": "PROCESSING",
        "estimatedTime": 60
    }
}
```

### 4. 查询导出状态
**接口地址：** `GET /exports/{exportId}/status`

**路径参数：**
- `exportId`：导出任务ID

**响应示例：**
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "exportId": "export_123456",
        "status": "COMPLETED",
        "progress": 100,
        "fileUrl": "https://file.company.com/exports/result.xlsx",
        "createdTime": "2024-01-01T12:00:00Z",
        "completedTime": "2024-01-01T12:01:30Z"
    }
}
```

## 错误码定义

### 系统级错误码（1000-1999）
| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1000 | 系统内部错误 | 服务器内部错误 |
| 1001 | 请求参数错误 | 请求参数格式或类型错误 |
| 1002 | 参数验证失败 | 参数不满足验证规则 |
| 1003 | 请求方法不支持 | HTTP方法不支持 |
| 1004 | 请求路径不存在 | API路径不存在 |
| 1005 | 请求频率超限 | 请求过于频繁 |

### 认证授权错误码（2000-2999）
| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 2001 | 未授权访问 | 缺少或无效的认证信息 |
| 2002 | 登录凭证已过期 | Token已过期 |
| 2003 | 权限不足 | 用户权限不足 |
| 2004 | 用户不存在 | 用户账号不存在 |
| 2005 | 密码错误 | 登录密码错误 |
| 2006 | 账号已被禁用 | 用户账号被禁用 |

### 业务逻辑错误码（3000-3999）
| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 3001 | 任务不存在 | 指定的任务不存在 |
| 3002 | 任务名称已存在 | 任务名称重复 |
| 3003 | 条目不存在 | 指定的条目不存在 |
| 3004 | 条目编号已存在 | 条目编号在任务中重复 |
| 3005 | 应答不存在 | 指定的应答不存在 |
| 3006 | 条目正在应答中 | 条目状态为应答中，不可操作 |
| 3007 | 文件格式不支持 | 上传的文件格式不支持 |
| 3008 | 文件大小超限 | 上传的文件大小超过限制 |
| 3009 | 导入数据格式错误 | Excel导入数据格式错误 |
| 3010 | 标签不存在 | 指定的标签不存在 |

### 外部服务错误码（4000-4999）
| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 4001 | GBBS服务不可用 | GBBS数据源服务异常 |
| 4002 | AI服务不可用 | AI应答服务异常 |
| 4003 | 文件服务不可用 | 文件存储服务异常 |
| 4004 | 邮件服务不可用 | 邮件通知服务异常 |

## 接口调用示例

### 完整的任务创建和条目应答流程

#### 1. 用户登录
```bash
curl -X POST https://api.soc.company.com/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "userCode": "123456",
    "password": "encrypted_password"
  }'
```

#### 2. 创建任务
```bash
curl -X POST https://api.soc.company.com/api/v1/tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "taskName": "中国电信5G项目SOC应答",
    "country": "中国",
    "customer": "中国电信",
    "project": "5G核心网建设项目",
    "dataSource": "GBBS"
  }'
```

#### 3. 添加条目
```bash
curl -X POST https://api.soc.company.com/api/v1/tasks/1/items \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "itemCode": "REQ_001",
    "itemDescription": "系统应支持高可用性架构设计",
    "products": ["华为云Stack"],
    "autoResponse": true
  }'
```

#### 4. 启动AI应答
```bash
curl -X POST https://api.soc.company.com/api/v1/items/1/ai-response \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "product": "华为云Stack"
  }'
```

#### 5. 查询应答结果
```bash
curl -X GET https://api.soc.company.com/api/v1/responses/1 \
  -H "Authorization: Bearer {token}"
```

## 安全性设计

### 1. 数据验证
- 所有输入参数进行严格验证
- 防止SQL注入和XSS攻击
- 文件上传类型和大小限制

### 2. 访问控制
- 基于JWT的身份认证
- 细粒度的权限控制
- 操作日志记录

### 3. 数据保护
- HTTPS加密传输
- 敏感数据脱敏处理
- 定期安全审计

### 4. 防护措施
- 请求频率限制
- 异常请求监控
- 自动封禁机制

## 性能优化

### 1. 缓存策略
- 用户信息缓存
- 标签列表缓存
- 产品目录缓存

### 2. 分页优化
- 合理的分页大小限制
- 游标分页支持
- 索引优化

### 3. 异步处理
- AI应答异步处理
- 文件导入导出异步处理
- 批量操作异步处理

### 4. 监控告警
- 接口响应时间监控
- 错误率监控
- 系统资源监控

## 总结

本API设计文档基于SOC智能应答系统的业务需求，设计了完整的RESTful API接口规范。涵盖了用户管理、任务管理、条目应答、数据分析等核心功能，并考虑了安全性、性能优化、错误处理等方面的需求。

该设计具有以下特点：
- 遵循RESTful设计原则
- 统一的接口规范和响应格式
- 完善的错误码体系
- 细粒度的权限控制
- 良好的扩展性和维护性
- 支持高并发和大数据量处理
