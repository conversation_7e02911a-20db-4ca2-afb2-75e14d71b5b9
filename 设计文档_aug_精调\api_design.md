# SOC智能应答系统 - API接口设计文档

## 目录
- [1. 概述](#1-概述)
- [2. 接口设计原则](#2-接口设计原则)
- [3. 通用规范](#3-通用规范)
- [4. 用户认证接口](#4-用户认证接口)
- [5. 任务管理接口](#5-任务管理接口)
- [6. 条目管理接口](#6-条目管理接口)
- [7. 应答管理接口](#7-应答管理接口)
- [8. AI匹配接口](#8-ai匹配接口)
- [9. 标签管理接口](#9-标签管理接口)
- [10. 数据分析接口](#10-数据分析接口)
- [11. 快捷应答接口](#11-快捷应答接口)
- [12. 文件管理接口](#12-文件管理接口)
- [13. Agent交互接口](#13-agent交互接口)
- [14. 系统配置接口](#14-系统配置接口)
- [15. 错误码和异常处理](#15-错误码和异常处理)
- [16. 安全性设计](#16-安全性设计)

## 1. 概述

### 1.1 系统简介
SOC智能应答系统提供一套完整的RESTful API，支持标书应答的全流程管理，包括任务创建、条目管理、AI智能匹配、人工应答、数据分析等核心功能。

### 1.2 技术栈
- **架构风格**: RESTful API
- **协议**: HTTPS
- **数据格式**: JSON
- **认证方式**: JWT Token
- **版本控制**: URL路径版本控制 (/api/v1/)

### 1.3 基础URL
- **生产环境**: `https://api.soc.example.com`
- **测试环境**: `https://api-test.soc.example.com`
- **开发环境**: `https://api-dev.soc.example.com`

## 2. 接口设计原则

### 2.1 RESTful设计原则
1. **资源导向**: URL以资源为中心，使用名词复数形式
2. **HTTP方法语义化**: 
   - GET: 查询资源
   - POST: 创建资源
   - PUT: 完整更新资源
   - PATCH: 部分更新资源
   - DELETE: 删除资源
3. **状态码语义化**: 使用标准HTTP状态码
4. **无状态**: API应该是无状态的

### 2.2 命名规范
- **URL路径**: 使用小写字母和短横线分隔
- **查询参数**: 使用驼峰命名(camelCase)
- **JSON字段**: 使用驼峰命名(camelCase)
- **常量**: 使用大写字母和下划线

### 2.3 响应格式统一
```json
{
  "code": 200,
  "message": "成功",
  "data": {},
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_123456789"
}
```

## 3. 通用规范

### 3.1 请求头
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-Request-ID: {unique_request_id}
User-Agent: {client_info}
```

### 3.2 分页参数
```json
{
  "current": 1,     // 当前页码
  "pageSize": 20,   // 每页数量
  "total": 100,     // 总记录数
  "pages": 5        // 总页数
}
```

### 3.3 排序参数
```
sortBy=createdTime&sortOrder=desc
```

### 3.4 筛选参数
```
filter[status]=active&filter[assignedTo]=123
```

## 4. 用户认证接口

### 4.1 用户登录
```http
POST /api/v1/auth/login
```

**请求参数:**
```json
{
  "username": "string",    // 用户名 (必填)
  "password": "string"     // 密码 (必填)
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_string",
    "expiresIn": 7200,
    "user": {
      "id": 1,
      "username": "zhangsan",
      "displayName": "张三",
      "employeeId": "EMP001",
      "email": "<EMAIL>",
      "department": "技术部",
      "permissions": ["soc:read", "soc:write"]
    }
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_login_001"
}
```

### 4.2 刷新Token
```http
POST /api/v1/auth/refresh
```

**请求参数:**
```json
{
  "refreshToken": "string"  // 刷新令牌 (必填)
}
```

### 4.3 用户登出
```http
POST /api/v1/auth/logout
```

### 4.4 获取用户信息
```http
GET /api/v1/auth/profile
```

## 5. 任务管理接口

### 5.1 任务列表查询
```http
GET /api/v1/tasks
```

**查询参数:**
```
?current=1&pageSize=20
&taskCode=TASK001
&taskName=测试任务
&country=中国
&customer=中国电信
&project=5G项目
&status=active
&createdBy=123
&sortBy=createdTime&sortOrder=desc
```

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "taskCode": "TASK001",
        "taskName": "5G基站建设项目应答",
        "countryMto": "中国",
        "mtoBranch": "广东分公司",
        "customer": "中国电信",
        "project": "5G基站建设",
        "dataSource": "GBBS",
        "totalItems": 156,
        "completedItems": 89,
        "fcCount": 45,
        "pcCount": 32,
        "ncCount": 12,
        "satisfactionRate": 86.52,
        "status": "active",
        "isPersonal": false,
        "createdBy": {
          "id": 123,
          "displayName": "张三",
          "employeeId": "EMP001"
        },
        "createdTime": "2024-12-19T08:00:00Z",
        "updatedTime": "2024-12-19T10:25:00Z"
      }
    ],
    "pagination": {
      "current": 1,
      "pageSize": 20,
      "total": 56,
      "pages": 3
    }
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_tasks_list_001"
}
```

### 5.2 创建任务
```http
POST /api/v1/tasks
```

**请求参数:**
```json
{
  "taskName": "5G基站建设项目应答",        // 任务名称 (必填)
  "countryMto": "中国",                      // 国家/MTO (可选)
  "mtoBranch": "广东分公司",                // MTO分支 (可选)
  "customer": "中国电信",                    // 客户 (可选)
  "project": "5G基站建设",                   // 项目 (可选)
  "dataSource": "GBBS",                      // 数据源 (必填)
  "itemFileId": 456,                         // 条目文件ID (可选)
  "autoResponse": true                       // 是否自动应答 (可选)
}
```

**响应示例:**
```json
{
  "code": 201,
  "message": "任务创建成功",
  "data": {
    "id": 123,
    "taskCode": "TASK202412190001",
    "taskName": "5G基站建设项目应答",
    "status": "active",
    "createdTime": "2024-12-19T10:30:00Z"
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_task_create_001"
}
```

### 5.3 获取任务详情
```http
GET /api/v1/tasks/{taskId}
```

### 5.4 更新任务
```http
PUT /api/v1/tasks/{taskId}
```

### 5.5 复制任务
```http
POST /api/v1/tasks/{taskId}/copy
```

**请求参数:**
```json
{
  "taskName": "新任务名称",                    // 新任务名称 (必填)
  "copyResponseData": false                   // 是否复制应答数据 (可选)
}
```

### 5.6 删除任务
```http
DELETE /api/v1/tasks/{taskId}
```

## 6. 条目管理接口

### 6.1 条目列表查询
```http
GET /api/v1/tasks/{taskId}/items
```

**查询参数:**
```
?current=1&pageSize=20
&itemCode=ITEM001
&itemDescription=基站建设
&productId=123
&status=pending
&satisfaction=FC
&assignedTo=456
&responseMethod=ai
&responseSource=GBBS
&tags=网络建设
&sortBy=createdTime&sortOrder=desc
```

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1001,
        "itemCode": "ITEM001",
        "itemDescription": "基站建设需求对环境影响评估",
        "additionalInfo": "需要提供详细的环评报告",
        "product": {
          "id": 123,
          "productCode": "5G_BASE_STATION",
          "productName": "5G基站设备"
        },
        "assignedTo": {
          "id": 456,
          "displayName": "李四",
          "employeeId": "EMP002"
        },
        "status": "completed",
        "satisfaction": "FC",
        "responseMethod": "ai",
        "responseSource": "GBBS",
        "responseContent": "我公司在5G基站建设阶段严格遵循国家环保法规...",
        "responseIndex": "GBBS-ENV-001",
        "tags": ["5G", "基站", "环保"],
        "remark": "此项需要重点关注",
        "similarityScore": 95.5,
        "version": 2,
        "createdTime": "2024-12-19T08:15:00Z",
        "updatedTime": "2024-12-19T10:20:00Z",
        "updatedBy": {
          "id": 456,
          "displayName": "李四"
        }
      }
    ],
    "pagination": {
      "current": 1,
      "pageSize": 20,
      "total": 156,
      "pages": 8
    }
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_items_list_001"
}
```

### 6.2 创建条目
```http
POST /api/v1/tasks/{taskId}/items
```

**请求参数:**
```json
{
  "itemCode": "ITEM001",                      // 条目编号 (必填)
  "itemDescription": "基站建设需求对环境影响评估", // 条目描述 (必填)
  "additionalInfo": "需要提供详细的环评报告",  // 补充信息 (可选)
  "productId": 123,                           // 产品ID (可选)
  "assignedTo": 456,                          // 指派给 (可选)
  "tags": ["5G", "基站", "环保"],           // 标签 (可选)
  "satisfaction": "FC",                       // 满足度 (可选)
  "responseContent": "我公司在5G基站...",    // 应答内容 (可选)
  "remark": "此项需要重点关注",            // 备注 (可选)
  "autoResponse": true,                       // 自动应答 (可选)
  "overwriteOnDuplicate": true                // 重复时覆盖 (可选)
}
```

### 6.3 批量创建条目 (批量导入)
```http
POST /api/v1/tasks/{taskId}/items/batch
```

**请求参数:**
```json
{
  "items": [
    {
      "itemCode": "ITEM001",
      "itemDescription": "条目描述",
      "productId": 123,
      "assignedTo": 456
    }
  ],
  "overwriteOnDuplicate": true                // 重复时覆盖
}
```

### 6.4 更新条目
```http
PUT /api/v1/items/{itemId}
```

### 6.5 删除条目
```http
DELETE /api/v1/items/{itemId}
```

### 6.6 批量操作
```http
POST /api/v1/tasks/{taskId}/items/batch-operation
```

**请求参数:**
```json
{
  "operation": "start_response",              // 操作类型: start_response, delete, add_tags, remove_tags, assign, set_product
  "itemIds": [1001, 1002, 1003],             // 条目 ID列表 (可选，空表示全部)
  "params": {                                 // 操作参数
    "assignedTo": 789,                       // 指派给 (仅assign操作需要)
    "productId": 456,                        // 产品ID (仅set_product操作需要)
    "tags": ["5G", "基站"]                 // 标签 (仅add_tags/remove_tags操作需要)
  }
}
```

### 6.7 条目导出
```http
GET /api/v1/tasks/{taskId}/items/export
```

**查询参数:**
```
?productIds=123,456&format=excel&includeHistory=false
```

## 7. 应答管理接口

### 7.1 获取条目应答详情
```http
GET /api/v1/items/{itemId}/response
```

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "item": {
      "id": 1001,
      "itemCode": "ITEM001",
      "itemDescription": "基站建设需求对环境影响评估",
      "additionalInfo": "需要提供详细的环评报告",
      "product": {
        "id": 123,
        "productName": "5G基站设备"
      },
      "task": {
        "countryMto": "中国",
        "mtoBranch": "广东分公司",
        "customer": "中国电信",
        "project": "5G基站建设"
      }
    },
    "currentResponse": {
      "satisfaction": "FC",
      "responseMethod": "ai",
      "responseSource": "GBBS",
      "responseContent": "我公司在5G基站建设阶段严格遵循国家环保法规...",
      "responseIndex": "GBBS-ENV-001",
      "remark": "此项需要重点关注",
      "version": 2,
      "updatedTime": "2024-12-19T10:20:00Z",
      "updatedBy": {
        "id": 456,
        "displayName": "李四"
      }
    },
    "historyVersions": [
      {
        "version": 1,
        "satisfaction": "PC",
        "responseMethod": "ai",
        "responseContent": "初始AI应答内容...",
        "operationType": "create",
        "createdTime": "2024-12-19T08:15:00Z",
        "createdBy": {
          "displayName": "系统自动"
        }
      }
    ]
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_response_detail_001"
}
```

### 7.2 保存应答结果
```http
PUT /api/v1/items/{itemId}/response
```

**请求参数:**
```json
{
  "satisfaction": "FC",                       // 满足度 (必填)
  "responseContent": "我公司在5G基站...",    // 应答内容 (必填)
  "responseIndex": "GBBS-ENV-001",            // 索引信息 (可选)
  "remark": "此项需要重点关注",            // 备注 (可选)
  "additionalInfo": "补充信息"              // 补充信息 (可选)
}
```

### 7.3 AI增强功能
```http
POST /api/v1/items/{itemId}/ai-enhance
```

**请求参数:**
```json
{
  "type": "polish",                          // 增强类型: polish-润色, translate-翻译
  "content": "原始应答内容",               // 需要增强的内容
  "targetLanguage": "en"                     // 目标语言 (仅翻译时需要)
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "AI增强成功",
  "data": {
    "originalContent": "原始应答内容",
    "enhancedContent": "增强后的应答内容",
    "type": "polish",
    "suggestions": [
      "建议1: 表达更加专业",
      "建议2: 增加数据支撑"
    ]
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_ai_enhance_001"
}
```

## 8. AI匹配接口

### 8.1 获取匹配详情
```http
GET /api/v1/items/{itemId}/matches
```

**查询参数:**
```
?dataSource=GBBS&satisfaction=FC&minMatchScore=80&current=1&pageSize=10
```

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "dataSources": [
      {
        "sourceType": "GBBS",
        "sourceName": "GBBS系统",
        "totalCount": 25,
        "fcCount": 15,
        "pcCount": 8,
        "ncCount": 2,
        "matches": [
          {
            "id": 2001,
            "matchScore": 95.5,
            "countryMatch": true,
            "branchMatch": true,
            "customerMatch": true,
            "sourceItemCode": "GBBS-ENV-001",
            "sourceDescription": "基站建设环境影响评估要求",
            "sourceSatisfaction": "FC",
            "sourceContent": "我公司在5G基站建设阶段严格遵循国家环保法规...",
            "sourceIndex": "GBBS-ENV-001",
            "sourceUrl": "https://gbbs.example.com/items/ENV-001",
            "isApplied": true,
            "appliedTime": "2024-12-19T09:30:00Z",
            "rankOrder": 1
          }
        ]
      }
    ],
    "pagination": {
      "current": 1,
      "pageSize": 10,
      "total": 25,
      "pages": 3
    }
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_matches_001"
}
```

### 8.2 应用匹配结果
```http
POST /api/v1/items/{itemId}/apply-match
```

**请求参数:**
```json
{
  "matchId": 2001,                           // 匹配结果ID (必填)
  "overwrite": true                          // 是否覆盖现有应答 (可选)
}
```

### 8.3 触发AI应答
```http
POST /api/v1/items/{itemId}/ai-response
```

**请求参数:**
```json
{
  "forceRefresh": false,                     // 是否强制刷新 (可选)
  "matchSources": ["GBBS"],                  // 匹配数据源 (可选)
  "minMatchScore": 70                        // 最低匹配度 (可选)
}
```

## 9. 标签管理接口

### 9.1 标签列表查询
```http
GET /api/v1/tags
```

**查询参数:**
```
?keyword=5G&status=1&isSystem=0&sortBy=useCount&sortOrder=desc
```

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "tagName": "5G",
      "tagColor": "#1890ff",
      "tagDescription": "5G相关技术",
      "useCount": 156,
      "isSystem": false,
      "status": 1,
      "createdTime": "2024-12-19T08:00:00Z"
    },
    {
      "id": 2,
      "tagName": "基站",
      "tagColor": "#52c41a",
      "tagDescription": "基站建设相关",
      "useCount": 89,
      "isSystem": false,
      "status": 1,
      "createdTime": "2024-12-19T08:05:00Z"
    }
  ],
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_tags_list_001"
}
```

### 9.2 创建标签
```http
POST /api/v1/tags
```

**请求参数:**
```json
{
  "tagName": "IoT",                          // 标签名称 (必填)
  "tagColor": "#722ed1",                     // 标签颜色 (可选)
  "tagDescription": "物联网相关技术"      // 标签描述 (可选)
}
```

### 9.3 更新标签
```http
PUT /api/v1/tags/{tagId}
```

### 9.4 删除标签
```http
DELETE /api/v1/tags/{tagId}
```

## 10. 数据分析接口

### 10.1 任务统计分析
```http
GET /api/v1/tasks/{taskId}/analytics
```

**查询参数:**
```
?assignedTo=456&dimension=product
```

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "overview": {
      "totalItems": 156,
      "completedItems": 89,
      "pendingItems": 45,
      "processingItems": 22,
      "completionRate": 57.05,
      "fcCount": 45,
      "pcCount": 32,
      "ncCount": 12,
      "satisfactionRate": 86.52
    },
    "productStats": [
      {
        "product": {
          "id": 123,
          "productCode": "5G_BASE_STATION",
          "productName": "5G基站设备"
        },
        "totalItems": 78,
        "completedItems": 45,
        "fcCount": 25,
        "pcCount": 15,
        "ncCount": 5,
        "satisfactionRate": 88.89
      },
      {
        "product": {
          "id": 124,
          "productCode": "OPTICAL_TRANSMISSION",
          "productName": "光传输设备"
        },
        "totalItems": 78,
        "completedItems": 44,
        "fcCount": 20,
        "pcCount": 17,
        "ncCount": 7,
        "satisfactionRate": 84.09
      }
    ],
    "timeSeriesData": {
      "dates": ["2024-12-15", "2024-12-16", "2024-12-17", "2024-12-18", "2024-12-19"],
      "completedItems": [15, 23, 34, 56, 89],
      "satisfactionRates": [85.2, 86.1, 85.8, 86.3, 86.5]
    }
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_analytics_001"
}
```

### 10.2 用户个人统计
```http
GET /api/v1/users/{userId}/analytics
```

**查询参数:**
```
?startDate=2024-12-01&endDate=2024-12-19&taskIds=123,124
```

## 11. 快捷应答接口

### 11.1 快捷应答
```http
POST /api/v1/quick-answer
```

**请求参数:**
```json
{
  "itemDescription": "基站建设需求对环境影响评估", // 条目描述 (必填)
  "productId": 123,                           // 产品ID (必填)
  "dataSource": "GBBS",                       // 数据源 (必填)
  "countryMto": "中国",                      // 国家/MTO (可选)
  "mtoBranch": "广东分公司",                // MTO分支 (可选)
  "customer": "中国电信",                    // 客户 (可选)
  "additionalInfo": "需要提供详细的环评报告"  // 补充信息 (可选)
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "快捷应答成功",
  "data": {
    "taskId": 999,                            // 自动创建的个人任务ID
    "itemId": 9001,                           // 创建的条目ID
    "response": {
      "satisfaction": "FC",
      "responseContent": "我公司在5G基站建设阶段严格遵循国家环保法规...",
      "responseSource": "GBBS",
      "matchScore": 95.5
    },
    "redirectUrl": "/tasks/999/items/9001"     // 跳转地址
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_quick_answer_001"
}
```

## 12. 文件管理接口

### 12.1 文件上传
```http
POST /api/v1/files/upload
```

**请求参数:** (multipart/form-data)
```
file: [文件二进制数据]           // 文件 (必填)
businessType: "item_import"            // 业务类型 (必填)
relatedId: "123"                       // 关联业务ID (可选)
```

**响应示例:**
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "fileId": 456,
    "fileName": "items_20241219_103000.xlsx",
    "originalName": "条目导入模板.xlsx",
    "filePath": "/uploads/2024/12/19/items_20241219_103000.xlsx",
    "fileSize": 1024000,
    "fileType": "xlsx",
    "uploadTime": "2024-12-19T10:30:00Z"
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_file_upload_001"
}
```

### 12.2 文件下载
```http
GET /api/v1/files/{fileId}/download
```

### 12.3 获取导入模板
```http
GET /api/v1/files/template
```

**查询参数:**
```
?type=item_import
```

## 13. Agent交互接口

### 13.1 Agent对话
```http
POST /api/v1/agent/chat
```

**请求参数:**
```json
{
  "message": "帮我创建一个中国电信的5G项目任务",      // 用户消息 (必填)
  "sessionId": "session_123",                 // 会话 ID (可选)
  "context": {                                // 上下文信息 (可选)
    "currentTaskId": 123,
    "currentPage": "/tasks"
  }
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "对话成功",
  "data": {
    "sessionId": "session_123",
    "messageId": "msg_456",
    "response": {
      "type": "text",                         // 响应类型: text, action, card
      "content": "好的，我已经为您创建了一个名为'中国电信 5G项目'的任务，任务编号为TASK202412190002。您可以点击下方链接查看任务详情。",
      "actions": [                            // 可执行操作 (可选)
        {
          "type": "link",
          "text": "查看任务详情",
          "url": "/tasks/124"
        },
        {
          "type": "button",
          "text": "开始导入条目",
          "action": "import_items",
          "params": { "taskId": 124 }
        }
      ]
    },
    "tools": [                                // 使用的工具列表
      {
        "name": "create_task",
        "result": {
          "taskId": 124,
          "taskName": "中国电信 5G项目",
          "taskCode": "TASK202412190002"
        }
      }
    ]
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_agent_chat_001"
}
```

### 13.2 获取对话历史
```http
GET /api/v1/agent/sessions/{sessionId}/messages
```

### 13.3 Agent工具执行
```http
POST /api/v1/agent/tools/{toolName}/execute
```

**请求参数:**
```json
{
  "params": {                                // 工具参数
    "taskName": "中国电信 5G项目",
    "customer": "中国电信",
    "countryMto": "中国"
  },
  "sessionId": "session_123"               // 会话 ID (可选)
}
```

## 14. 系统配置接口

### 14.1 产品列表查询
```http
GET /api/v1/config/products
```

**查询参数:**
```
?type=tree&level=2&status=1
```

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "productCode": "5G_PRODUCTS",
      "productName": "5G产品系列",
      "productType": "category",
      "level": 1,
      "children": [
        {
          "id": 123,
          "productCode": "5G_BASE_STATION",
          "productName": "5G基站设备",
          "productType": "product",
          "level": 2,
          "description": "5G网络基础设备",
          "hasPermission": true
        }
      ]
    }
  ],
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_products_001"
}
```

### 14.2 数据源配置查询
```http
GET /api/v1/config/data-sources
```

### 14.3 用户权限查询
```http
GET /api/v1/users/{userId}/permissions
```

**响应示例:**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "userId": 123,
    "productPermissions": [
      {
        "productId": 123,
        "productCode": "5G_BASE_STATION",
        "productName": "5G基站设备",
        "permissionType": "write",
        "expireTime": "2025-12-31T23:59:59Z"
      }
    ],
    "systemPermissions": [
      "soc:task:create",
      "soc:item:read",
      "soc:item:write",
      "soc:response:read",
      "soc:response:write"
    ]
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_permissions_001"
}
```

## 15. 错误码和异常处理

### 15.1 错误码定义

#### 全局错误码
- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权或Token过期
- `403`: 禁止访问，权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `422`: 业务校验失败
- `429`: 请求频率过高
- `500`: 服务器内部错误
- `502`: 网关错误
- `503`: 服务不可用

#### 业务错误码
- `10001`: 用户名或密码错误
- `10002`: 用户已被禁用
- `10003`: Token已过期
- `20001`: 任务名称已存在
- `20002`: 任务不存在或无权访问
- `30001`: 条目编号重复
- `30002`: 条目正在应答中，无法编辑
- `40001`: 产品权限不足
- `40002`: 文件格式不支持
- `50001`: AI服务不可用
- `50002`: 数据源连接失败

### 15.2 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "taskName",
        "message": "任务名称不能为空",
        "code": "REQUIRED"
      },
      {
        "field": "taskName",
        "message": "任务名称长度不能超过200个字符",
        "code": "MAX_LENGTH"
      }
    ]
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_error_001",
  "path": "/api/v1/tasks"
}
```

### 15.3 异常情况处理

#### 数据校验失败 (422)
```json
{
  "code": 422,
  "message": "业务校验失败",
  "data": null,
  "error": {
    "type": "BUSINESS_VALIDATION_ERROR",
    "code": "ITEM_CODE_DUPLICATE",
    "message": "条目编号ITEM001在产品5G基站设备下已存在",
    "details": {
      "itemCode": "ITEM001",
      "productId": 123,
      "existingItemId": 1001
    }
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_validation_error_001"
}
```

#### 权限不足 (403)
```json
{
  "code": 403,
  "message": "禁止访问，权限不足",
  "data": null,
  "error": {
    "type": "PERMISSION_DENIED",
    "code": "PRODUCT_ACCESS_DENIED",
    "message": "您没有产品5G基站设备的访问权限，请联系管理员申请权限",
    "details": {
      "requiredPermission": "product:123:write",
      "productName": "5G基站设备",
      "applyUrl": "https://it.example.com/permissions/apply"
    }
  },
  "timestamp": "2024-12-19T10:30:00Z",
  "requestId": "req_permission_error_001"
}
```

## 16. 安全性设计

### 16.1 认证机制
1. **JWT Token**: 使用JWT作为主要认证方式
2. **Token过期**: 访问令牌2小时，刷新令牌7天
3. **双 Token机制**: 访问令牌 + 刷新令牌
4. **Token黑名单**: 支持Token强制失效

### 16.2 权限控制
1. **粒度权限**: 基于产品的细粒度权限控制
2. **资源隔离**: 用户只能访问授权的资源
3. **操作权限**: 区分读取和写入权限
4. **数据范围**: 任务创建为、条目指派人权限控制

### 16.3 数据安全
1. **数据加密**: 敏感数据传输和存储加密
2. **参数校验**: 严格的输入参数校验
3. **SQL防注入**: 使用参数化查询防止SQL注入
4. **XSS防护**: 输出数据转义处理

### 16.4 接口安全
1. **HTTPS强制**: 所有接口强制使用HTTPS
2. **频率限制**: 防止接口被滥用
3. **请求签名**: 重要接口支持请求签名验证
4. **CORS配置**: 严格的跨域访问控制

### 16.5 审计日志
1. **操作日志**: 记录所有关键操作
2. **访问日志**: 记录API访问详情
3. **安全日志**: 记录安全相关事件
4. **日志存储**: 日志集中存储和分析

### 16.6 安全头配置
```http
# 安全响应头
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
Referrer-Policy: strict-origin-when-cross-origin
```

---

**注意事项：**
1. 本接口设计遵循RESTful规范，确保接口语义化和一致性
2. 所有接口均需要身份认证，除非特殊说明
3. 生产环境部署时请配置合适的限流和缓存策略
4. 定期更新API文档，保持与实际实现一致
3. [通用规范](#通用规范)
4. [认证与授权](#认证与授权)
5. [错误码体系](#错误码体系)
6. [任务管理接口](#任务管理接口)
7. [条目管理接口](#条目管理接口)
8. [应答管理接口](#应答管理接口)
9. [数据分析接口](#数据分析接口)
10. [系统管理接口](#系统管理接口)

## 概述

本文档基于SOC智能应答系统的需求文档，设计了完整的RESTful API接口。系统采用前后端分离架构，通过HTTP/HTTPS协议提供服务。

### 核心功能模块
- **任务管理**：任务的创建、查询、编辑、删除等操作
- **条目管理**：条目的批量导入、查询、应答、标签管理等
- **应答管理**：AI应答、人工应答、匹配详情查看等
- **数据分析**：任务进度统计、满足度分析等
- **系统管理**：用户管理、产品管理、权限控制等

## 接口设计原则

1. **RESTful设计**：遵循REST架构风格，使用标准HTTP方法
2. **版本控制**：通过URL路径进行版本管理（/api/v1/）
3. **统一响应**：标准化的响应格式和错误处理
4. **安全性**：完善的认证授权和数据验证机制
5. **性能优化**：支持分页、筛选、排序等查询优化
6. **幂等性**：确保相同请求的幂等性

## 通用规范

### 基础URL
```
生产环境：https://api.soc.company.com/api/v1
测试环境：https://api-test.soc.company.com/api/v1
开发环境：http://localhost:8080/api/v1
```

### HTTP方法使用规范
- **GET**：查询数据，不修改服务器状态
- **POST**：创建新资源
- **PUT**：完整更新资源
- **PATCH**：部分更新资源
- **DELETE**：删除资源

### 请求头规范
```http
Content-Type: application/json
Authorization: Bearer {access_token}
X-Request-ID: {unique_request_id}
X-Client-Version: {client_version}
Accept-Language: zh-CN,en-US
```

### 响应格式规范
#### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据内容
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "requestId": "req_123456789"
}
```

#### 分页响应
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            // 数据列表
        ],
        "pagination": {
            "current": 1,
            "pageSize": 20,
            "total": 100,
            "totalPages": 5
        }
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "requestId": "req_123456789"
}
```

#### 错误响应
```json
{
    "code": 400,
    "message": "请求参数错误",
    "error": {
        "type": "VALIDATION_ERROR",
        "details": [
            {
                "field": "taskName",
                "message": "任务名称不能为空"
            }
        ]
    },
    "timestamp": "2024-01-01T12:00:00Z",
    "requestId": "req_123456789"
}
```

### 分页参数规范
```json
{
    "current": 1,        // 当前页码，从1开始
    "pageSize": 20,      // 每页大小，默认20，最大200
    "sortField": "createdTime",  // 排序字段
    "sortOrder": "desc"  // 排序方向：asc/desc
}
```

## 认证与授权

### JWT Token认证
系统采用JWT Token进行用户认证，Token包含用户基本信息和权限信息。

#### 登录接口
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "userCode": "123456",
    "password": "encrypted_password"
}
```

#### 响应
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 7200,
        "user": {
            "id": 1,
            "userCode": "123456",
            "userName": "张三",
            "role": "USER",
            "permissions": ["TASK_CREATE", "ITEM_MANAGE"]
        }
    }
}
```

### 权限控制
- **任务创建人**：拥有任务的完整操作权限
- **条目指派人**：拥有指派条目的操作权限
- **系统管理员**：拥有所有权限

## 错误码体系

### HTTP状态码
- **200**：请求成功
- **201**：创建成功
- **400**：请求参数错误
- **401**：未认证
- **403**：无权限
- **404**：资源不存在
- **409**：资源冲突
- **422**：数据验证失败
- **500**：服务器内部错误

### 业务错误码
| 错误码 | 错误类型 | 描述 |
|--------|----------|------|
| 10001 | VALIDATION_ERROR | 参数验证失败 |
| 10002 | RESOURCE_NOT_FOUND | 资源不存在 |
| 10003 | RESOURCE_CONFLICT | 资源冲突 |
| 10004 | PERMISSION_DENIED | 权限不足 |
| 10005 | OPERATION_FAILED | 操作失败 |
| 20001 | TASK_NOT_FOUND | 任务不存在 |
| 20002 | TASK_NAME_DUPLICATE | 任务名称重复 |
| 20003 | TASK_STATUS_INVALID | 任务状态无效 |
| 30001 | ITEM_NOT_FOUND | 条目不存在 |
| 30002 | ITEM_CODE_DUPLICATE | 条目编号重复 |
| 30003 | ITEM_STATUS_INVALID | 条目状态无效 |
| 40001 | RESPONSE_NOT_FOUND | 应答不存在 |
| 40002 | RESPONSE_IN_PROGRESS | 应答进行中 |
| 50001 | AI_SERVICE_ERROR | AI服务异常 |
| 50002 | DATA_SOURCE_ERROR | 数据源异常 |

## 任务管理接口

### 1. 创建任务
创建新的SOC应答任务

**接口地址**：`POST /api/v1/tasks`

**请求参数**：
```json
{
    "taskName": "中国电信5G项目SOC应答",
    "country": "中国",
    "mtoBranch": "广东分公司",
    "customer": "中国电信",
    "project": "5G核心网建设项目",
    "dataSource": "GBBS",
    "itemFile": {
        "fileName": "应答条目.xlsx",
        "fileUrl": "https://file.example.com/items.xlsx"
    }
}
```

**字段验证规则**：
- `taskName`：必填，长度1-200字符，不能重复
- `country`：可选，长度1-100字符
- `mtoBranch`：可选，长度1-100字符
- `customer`：可选，长度1-200字符
- `project`：可选，长度1-200字符
- `dataSource`：必填，枚举值：GBBS/文档库/项目文档/历史SOC文档

**响应示例**：
```json
{
    "code": 201,
    "message": "任务创建成功",
    "data": {
        "id": 1,
        "taskCode": "TASK_20240101_001",
        "taskName": "中国电信5G项目SOC应答",
        "country": "中国",
        "mtoBranch": "广东分公司",
        "customer": "中国电信",
        "project": "5G核心网建设项目",
        "dataSource": "GBBS",
        "creatorId": 1,
        "status": 1,
        "totalItems": 0,
        "answeredItems": 0,
        "satisfactionRate": 0.00,
        "createdTime": "2024-01-01T12:00:00Z"
    }
}
```

### 2. 查询任务列表
分页查询任务列表，支持多条件筛选

**接口地址**：`GET /api/v1/tasks`

**查询参数**：
```
taskCode: 任务编码（精确查询）
taskName: 任务名称（模糊查询）
country: 国家（精确查询）
customer: 客户（精确查询）
project: 项目（精确查询）
status: 状态（精确查询）
current: 当前页码，默认1
pageSize: 每页大小，默认20
sortField: 排序字段，默认createdTime
sortOrder: 排序方向，默认desc
```

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "taskCode": "TASK_20240101_001",
                "taskName": "中国电信5G项目SOC应答",
                "country": "中国",
                "customer": "中国电信",
                "project": "5G核心网建设项目",
                "totalItems": 150,
                "answeredItems": 120,
                "progress": "120/150",
                "satisfactionRate": 85.50,
                "creatorName": "张三",
                "createdTime": "2024-01-01T12:00:00Z",
                "updatedTime": "2024-01-01T15:30:00Z"
            }
        ],
        "pagination": {
            "current": 1,
            "pageSize": 20,
            "total": 1,
            "totalPages": 1
        }
    }
}

### 3. 获取任务详情
根据任务ID获取任务详细信息

**接口地址**：`GET /api/v1/tasks/{taskId}`

**路径参数**：
- `taskId`：任务ID，必填

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "id": 1,
        "taskCode": "TASK_20240101_001",
        "taskName": "中国电信5G项目SOC应答",
        "country": "中国",
        "mtoBranch": "广东分公司",
        "customer": "中国电信",
        "project": "5G核心网建设项目",
        "dataSource": "GBBS",
        "creatorId": 1,
        "creatorName": "张三",
        "status": 1,
        "totalItems": 150,
        "answeredItems": 120,
        "fcCount": 80,
        "pcCount": 30,
        "ncCount": 10,
        "satisfactionRate": 85.50,
        "createdTime": "2024-01-01T12:00:00Z",
        "updatedTime": "2024-01-01T15:30:00Z"
    }
}
```

### 4. 编辑任务
更新任务基本信息

**接口地址**：`PUT /api/v1/tasks/{taskId}`

**权限要求**：任务创建人

**请求参数**：
```json
{
    "taskName": "中国电信5G项目SOC应答-更新版",
    "country": "中国",
    "mtoBranch": "广东分公司",
    "customer": "中国电信",
    "project": "5G核心网建设项目二期",
    "dataSource": "GBBS"
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "任务更新成功",
    "data": {
        "id": 1,
        "taskName": "中国电信5G项目SOC应答-更新版",
        "updatedTime": "2024-01-01T16:00:00Z"
    }
}
```

### 5. 复制任务
基于现有任务创建副本

**接口地址**：`POST /api/v1/tasks/{taskId}/copy`

**权限要求**：任务创建人或条目指派人

**请求参数**：
```json
{
    "taskName": "中国电信5G项目SOC应答_复制",
    "copyResponses": true  // 是否复制应答结果
}
```

**响应示例**：
```json
{
    "code": 201,
    "message": "任务复制成功",
    "data": {
        "id": 2,
        "taskCode": "TASK_20240101_002",
        "taskName": "中国电信5G项目SOC应答_复制",
        "originalTaskId": 1,
        "createdTime": "2024-01-01T16:30:00Z"
    }
}
```

### 6. 删除任务
删除指定任务及其关联数据

**接口地址**：`DELETE /api/v1/tasks/{taskId}`

**权限要求**：任务创建人

**响应示例**：
```json
{
    "code": 200,
    "message": "任务删除成功",
    "data": null
}
```

## 条目管理接口

### 1. 查询条目列表
分页查询任务下的条目列表

**接口地址**：`GET /api/v1/tasks/{taskId}/items`

**查询参数**：
```
code: 条目编号（精确查询）
description: 条目描述（模糊查询）
productId: 产品ID（精确查询）
status: 应答状态（精确查询）1-未应答，2-应答中，3-已应答
tag: 标签（精确查询）
satisfaction: 满足度（精确查询）FC/PC/NC
assigneeId: 指派人ID（精确查询）
responseMethod: 应答方式（精确查询）AI/手工
dataSource: 应答来源（精确查询）
current: 当前页码，默认1
pageSize: 每页大小，默认20
sortField: 排序字段，默认updatedTime
sortOrder: 排序方向，默认desc
```

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "itemCode": "REQ_001",
                "itemDescription": "系统应支持用户身份认证功能",
                "products": [
                    {
                        "productId": 1,
                        "productName": "华为云Stack",
                        "status": 3,
                        "satisfaction": "FC",
                        "responseContent": "华为云Stack支持多种身份认证方式...",
                        "responseMethod": "AI",
                        "dataSource": "GBBS",
                        "sourceIndex": "GBBS-AUTH-001",
                        "matchScore": 95.5
                    }
                ],
                "tags": ["安全", "认证"],
                "assigneeId": 1,
                "assigneeName": "张三",
                "remark": "重要条目",
                "createdBy": 1,
                "updatedBy": 1,
                "updatedTime": "2024-01-01T15:30:00Z"
            }
        ],
        "pagination": {
            "current": 1,
            "pageSize": 20,
            "total": 150,
            "totalPages": 8
        }
    }
}
```

### 2. 单条录入条目
手动添加单个条目

**接口地址**：`POST /api/v1/tasks/{taskId}/items`

**权限要求**：任务创建人

**请求参数**：
```json
{
    "itemCode": "REQ_002",
    "itemDescription": "系统应支持数据加密存储",
    "supplementInfo": "需要支持AES-256加密算法",
    "productIds": [1, 2],
    "tags": ["安全", "加密"],
    "assigneeId": 1,
    "autoResponse": true,
    "overwriteOnDuplicate": true,
    "remark": "核心安全需求"
}
```

**字段验证规则**：
- `itemCode`：必填，长度1-100字符
- `itemDescription`：必填，长度1-65535字符
- `productIds`：可选，产品ID数组
- `tags`：可选，标签数组
- `autoResponse`：必填，布尔值
- `overwriteOnDuplicate`：必填，布尔值

**响应示例**：
```json
{
    "code": 201,
    "message": "条目创建成功",
    "data": {
        "id": 2,
        "itemCode": "REQ_002",
        "itemDescription": "系统应支持数据加密存储",
        "status": 2,  // 自动应答开启，状态为应答中
        "createdTime": "2024-01-01T16:00:00Z"
    }
}
```

### 3. 批量导入条目
通过Excel文件批量导入条目

**接口地址**：`POST /api/v1/tasks/{taskId}/items/batch-import`

**权限要求**：任务创建人

**请求参数**（multipart/form-data）：
```
file: Excel文件（必填）
overwriteOnDuplicate: 重复时是否覆盖（可选，默认false）
```

**Excel文件格式要求**：
- 支持.xls和.xlsx格式
- 必须包含列：编号、条目描述
- 可选列：标签、满足度、应答说明、补充信息

**响应示例**：
```json
{
    "code": 200,
    "message": "批量导入成功",
    "data": {
        "totalCount": 100,
        "successCount": 95,
        "failureCount": 5,
        "duplicateCount": 3,
        "failureDetails": [
            {
                "row": 10,
                "itemCode": "REQ_010",
                "error": "条目描述不能为空"
            }
        ]
    }
}
```

### 4. 开始应答
批量启动条目的AI应答

**接口地址**：`POST /api/v1/tasks/{taskId}/items/start-response`

**权限要求**：任务创建人或条目指派人

**请求参数**：
```json
{
    "itemIds": [1, 2, 3],  // 可选，不传则对所有条目操作
    "forceRestart": false  // 是否强制重新开始
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "应答启动成功",
    "data": {
        "totalCount": 3,
        "startedCount": 3,
        "skippedCount": 0,
        "taskId": "ai_task_123456"  // 异步任务ID
    }
}

### 5. 批量操作条目
对选中的条目进行批量操作

**接口地址**：`POST /api/v1/tasks/{taskId}/items/batch-operation`

**权限要求**：任务创建人或条目指派人

**请求参数**：
```json
{
    "operation": "ADD_TAG",  // 操作类型：ADD_TAG/REMOVE_TAG/SET_PRODUCT/ASSIGN_TO/DELETE
    "itemIds": [1, 2, 3],    // 可选，不传则对所有条目操作
    "params": {
        "tags": ["新标签1", "新标签2"],  // ADD_TAG/REMOVE_TAG时使用
        "productId": 1,                  // SET_PRODUCT时使用
        "assigneeId": 2                  // ASSIGN_TO时使用
    }
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "批量操作成功",
    "data": {
        "operation": "ADD_TAG",
        "totalCount": 3,
        "successCount": 3,
        "failureCount": 0
    }
}
```

### 6. 导出条目
导出任务的条目应答结果

**接口地址**：`POST /api/v1/tasks/{taskId}/items/export`

**权限要求**：任务创建人或条目指派人

**请求参数**：
```json
{
    "productIds": [1, 2],  // 可选，导出指定产品的条目
    "format": "EXCEL",     // 导出格式：EXCEL/PDF
    "includeImages": true  // 是否包含图片
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "导出任务创建成功",
    "data": {
        "taskId": "export_task_123456",
        "downloadUrl": "https://file.example.com/export/task_1_items.xlsx",
        "expiresAt": "2024-01-02T12:00:00Z"
    }
}
```

## 应答管理接口

### 1. 获取条目应答详情
获取指定条目的应答详细信息

**接口地址**：`GET /api/v1/items/{itemId}/responses`

**查询参数**：
```
productId: 产品ID（可选，不传则返回所有产品的应答）
version: 版本号（可选，不传则返回最新版本）
```

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "itemId": 1,
        "itemCode": "REQ_001",
        "itemDescription": "系统应支持用户身份认证功能",
        "supplementInfo": "需要支持多种认证方式",
        "responses": [
            {
                "id": 1,
                "productId": 1,
                "productName": "华为云Stack",
                "satisfaction": "FC",
                "responseContent": "<p>华为云Stack支持多种身份认证方式...</p>",
                "responseMethod": "AI",
                "dataSource": "GBBS",
                "sourceIndex": "GBBS-AUTH-001",
                "matchScore": 95.5,
                "version": 1,
                "status": 2,
                "createdBy": 1,
                "updatedBy": 1,
                "createdTime": "2024-01-01T14:00:00Z",
                "updatedTime": "2024-01-01T15:30:00Z"
            }
        ]
    }
}
```

### 2. 手工应答条目
手动编辑条目的应答内容

**接口地址**：`PUT /api/v1/items/{itemId}/responses/{responseId}`

**权限要求**：任务创建人或条目指派人

**请求参数**：
```json
{
    "satisfaction": "PC",
    "responseContent": "<p>华为云Stack部分支持身份认证功能...</p>",
    "sourceIndex": "手工编辑",
    "remark": "需要进一步确认技术细节",
    "changeReason": "根据最新技术规格调整"
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "应答更新成功",
    "data": {
        "id": 1,
        "version": 2,
        "satisfaction": "PC",
        "responseMethod": "手工",
        "updatedTime": "2024-01-01T16:00:00Z"
    }
}
```

### 3. AI应答条目
触发指定条目的AI应答

**接口地址**：`POST /api/v1/items/{itemId}/ai-response`

**权限要求**：任务创建人或条目指派人

**请求参数**：
```json
{
    "productId": 1,
    "supplementInfo": "请重点关注安全性要求",
    "forceRegenerate": false  // 是否强制重新生成
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "AI应答启动成功",
    "data": {
        "taskId": "ai_response_123456",
        "estimatedTime": 30  // 预估完成时间（秒）
    }
}
```

### 4. 获取匹配详情
获取AI应答的匹配详情信息

**接口地址**：`GET /api/v1/responses/{responseId}/matches`

**查询参数**：
```
dataSource: 数据源筛选（可选）
satisfaction: 满足度筛选（可选）FC/PC/NC
minMatchScore: 最小匹配度（可选）
current: 当前页码，默认1
pageSize: 每页大小，默认10
```

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "responseId": 1,
        "dataSources": [
            {
                "dataSource": "GBBS",
                "totalCount": 15,
                "fcCount": 8,
                "pcCount": 5,
                "ncCount": 2,
                "matches": [
                    {
                        "id": 1,
                        "sourceItemCode": "GBBS-AUTH-001",
                        "sourceDescription": "用户身份认证功能说明",
                        "sourceSatisfaction": "FC",
                        "sourceContent": "系统支持LDAP、AD等多种认证方式...",
                        "sourceIndex": "技术规格书-第3.2节",
                        "matchScore": 95.5,
                        "countryMatch": 1,
                        "branchMatch": 1,
                        "customerMatch": 0,
                        "isSelected": 1
                    }
                ]
            }
        ],
        "pagination": {
            "current": 1,
            "pageSize": 10,
            "total": 15,
            "totalPages": 2
        }
    }
}
```

### 5. 应用匹配结果
将选中的匹配结果应用到当前应答

**接口地址**：`POST /api/v1/responses/{responseId}/apply-match`

**权限要求**：任务创建人或条目指派人

**请求参数**：
```json
{
    "matchId": 1,
    "changeReason": "选择更合适的匹配结果"
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "匹配结果应用成功",
    "data": {
        "responseId": 1,
        "version": 2,
        "responseMethod": "AI",
        "updatedTime": "2024-01-01T16:30:00Z"
    }
}
```

### 6. 获取应答历史
查看应答的历史版本

**接口地址**：`GET /api/v1/responses/{responseId}/history`

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "responseId": 1,
        "currentVersion": 2,
        "history": [
            {
                "id": 1,
                "version": 1,
                "satisfaction": "FC",
                "responseContent": "<p>原始AI应答内容...</p>",
                "responseMethod": "AI",
                "changeReason": "初始AI应答",
                "createdBy": 1,
                "createdByName": "张三",
                "createdTime": "2024-01-01T14:00:00Z"
            },
            {
                "id": 2,
                "version": 2,
                "satisfaction": "PC",
                "responseContent": "<p>手工修改后的内容...</p>",
                "responseMethod": "手工",
                "changeReason": "根据最新技术规格调整",
                "createdBy": 1,
                "createdByName": "张三",
                "createdTime": "2024-01-01T16:00:00Z"
            }
        ]
    }
}

## 数据分析接口

### 1. 获取任务统计数据
获取任务的整体统计数据

**接口地址**：`GET /api/v1/tasks/{taskId}/statistics`

**查询参数**：
```
assigneeId: 指派人ID（可选，不传则返回所有人的统计）
```

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "taskId": 1,
        "totalItems": 150,
        "answeredItems": 120,
        "unansweredItems": 20,
        "inProgressItems": 10,
        "completionRate": 80.00,
        "fcCount": 80,
        "pcCount": 30,
        "ncCount": 10,
        "satisfactionRate": 85.50,
        "productStatistics": [
            {
                "productId": 1,
                "productName": "华为云Stack",
                "totalItems": 100,
                "answeredItems": 90,
                "fcCount": 60,
                "pcCount": 20,
                "ncCount": 10,
                "satisfactionRate": 88.89
            },
            {
                "productId": 2,
                "productName": "FusionSphere",
                "totalItems": 50,
                "answeredItems": 30,
                "fcCount": 20,
                "pcCount": 10,
                "ncCount": 0,
                "satisfactionRate": 100.00
            }
        ],
        "assigneeStatistics": [
            {
                "assigneeId": 1,
                "assigneeName": "张三",
                "totalItems": 100,
                "answeredItems": 80,
                "satisfactionRate": 85.00
            },
            {
                "assigneeId": 2,
                "assigneeName": "李四",
                "totalItems": 50,
                "answeredItems": 40,
                "satisfactionRate": 87.50
            }
        ]
    }
}
```

### 2. 获取标签分析数据
获取任务的标签分布统计

**接口地址**：`GET /api/v1/tasks/{taskId}/tag-analysis`

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "taskId": 1,
        "totalTags": 10,
        "tagDistribution": [
            {
                "tagId": 1,
                "tagName": "安全",
                "count": 30,
                "percentage": 20.00
            },
            {
                "tagId": 2,
                "tagName": "性能",
                "count": 25,
                "percentage": 16.67
            }
        ],
        "tagSatisfaction": [
            {
                "tagId": 1,
                "tagName": "安全",
                "fcCount": 20,
                "pcCount": 8,
                "ncCount": 2,
                "satisfactionRate": 93.33
            }
        ]
    }
}
```

## 系统管理接口

### 1. 获取产品列表
获取系统支持的产品列表

**接口地址**：`GET /api/v1/products`

**查询参数**：
```
keyword: 关键词搜索（可选）
status: 状态筛选（可选）1-启用，0-禁用
parentId: 父产品ID（可选）
```

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "productCode": "SOC_STANDARD",
                "productName": "SOC标准库",
                "productType": "STANDARD",
                "parentId": null,
                "level": 1,
                "sortOrder": 1,
                "status": 1,
                "children": [
                    {
                        "id": 4,
                        "productCode": "HUAWEI_CLOUD_STACK",
                        "productName": "华为云Stack",
                        "productType": "PRODUCT",
                        "parentId": 1,
                        "level": 2,
                        "sortOrder": 1,
                        "status": 1
                    }
                ]
            }
        ]
    }
}
```

### 2. 获取标签列表
获取系统中的标签列表

**接口地址**：`GET /api/v1/tags`

**查询参数**：
```
keyword: 关键词搜索（可选）
sortBy: 排序字段（可选）name/usage
```

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "list": [
            {
                "id": 1,
                "tagName": "安全",
                "tagColor": "#f50",
                "usageCount": 120
            },
            {
                "id": 2,
                "tagName": "性能",
                "tagColor": "#2db7f5",
                "usageCount": 85
            }
        ]
    }
}
```

### 3. 快捷应答接口
快速创建任务并应答条目

**接口地址**：`POST /api/v1/quick-response`

**请求参数**：
```json
{
    "dataSource": "GBBS",
    "productIds": [1, 2],
    "country": "中国",
    "mtoBranch": "广东分公司",
    "customer": "中国电信",
    "itemDescription": "系统应支持用户身份认证功能",
    "supplementInfo": "需要支持多种认证方式"
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "快捷应答启动成功",
    "data": {
        "taskId": 3,
        "taskCode": "PERSONAL_20240101_001",
        "itemId": 5,
        "responseTaskId": "quick_response_123456"
    }
}
```

### 4. 获取应答进度
查询异步应答任务的进度

**接口地址**：`GET /api/v1/response-tasks/{taskId}`

**响应示例**：
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "taskId": "ai_task_123456",
        "status": "PROCESSING",  // QUEUED/PROCESSING/COMPLETED/FAILED
        "progress": 60,  // 百分比
        "totalItems": 10,
        "processedItems": 6,
        "estimatedTimeRemaining": 120,  // 预估剩余时间（秒）
        "startTime": "2024-01-01T16:00:00Z",
        "message": "正在处理第6个条目..."
    }
}
```

## 接口调用示例

### 创建任务并批量导入条目
```javascript
// 1. 创建任务
const createTaskResponse = await fetch('https://api.soc.company.com/api/v1/tasks', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        taskName: '中国电信5G项目SOC应答',
        country: '中国',
        customer: '中国电信',
        project: '5G核心网建设项目',
        dataSource: 'GBBS'
    })
});

const taskData = await createTaskResponse.json();
const taskId = taskData.data.id;

// 2. 上传Excel文件
const formData = new FormData();
formData.append('file', excelFile);
formData.append('overwriteOnDuplicate', 'true');

const importResponse = await fetch(`https://api.soc.company.com/api/v1/tasks/${taskId}/items/batch-import`, {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
});

// 3. 启动AI应答
const startResponseResponse = await fetch(`https://api.soc.company.com/api/v1/tasks/${taskId}/items/start-response`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({})
});

// 4. 轮询应答进度
const responseTaskId = startResponseResponse.data.taskId;
const checkProgress = setInterval(async () => {
    const progressResponse = await fetch(`https://api.soc.company.com/api/v1/response-tasks/${responseTaskId}`, {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    });

    const progressData = await progressResponse.json();
    if (progressData.data.status === 'COMPLETED' || progressData.data.status === 'FAILED') {
        clearInterval(checkProgress);
        // 处理完成或失败逻辑
    }
}, 5000);
```

## 安全性设计

### 接口安全措施
1. **HTTPS加密**：所有接口通过HTTPS协议传输
2. **JWT认证**：基于JWT的用户认证和授权
3. **CSRF防护**：使用CSRF Token防止跨站请求伪造
4. **请求限流**：API限流防止恶意请求
5. **参数验证**：严格的请求参数验证
6. **敏感数据脱敏**：日志和响应中的敏感数据脱敏处理

### 权限控制策略
1. **基于角色**：ADMIN/USER角色区分
2. **基于资源**：任务创建人/条目指派人权限区分
3. **操作审计**：所有关键操作记录日志

---

**文档版本**：v1.0
**创建时间**：2024年
**更新时间**：2024年
**维护人员**：SOC智能应答系统开发团队
```
```
```
