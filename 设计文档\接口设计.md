# SOC智能应答系统 - 接口设计

## 1. 概述

本文档描述了SOC智能应答系统的RESTful API接口设计，包括任务管理、条目管理、AI应答、数据分析等功能模块。

## 2. 接口设计原则

- 遵循RESTful设计规范
- 统一的响应格式
- 完善的错误处理
- 支持分页查询
- 接口版本控制

## 3. 通用响应格式

### 3.1 成功响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 3.2 错误响应格式
```json
{
    "code": 400,
    "message": "参数错误",
    "error": "详细错误信息",
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 3.3 分页响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [],
        "pagination": {
            "current": 1,
            "pageSize": 20,
            "total": 100,
            "totalPages": 5
        }
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

## 4. 用户认证接口

### 4.1 用户登录
```
POST /api/v1/auth/login
```

**请求参数：**
```json
{
    "userCode": "string",
    "password": "string"
}
```

**响应数据：**
```json
{
    "token": "jwt_token",
    "userInfo": {
        "id": 1,
        "userCode": "U001",
        "userName": "张三",
        "email": "<EMAIL>",
        "department": "技术部",
        "permissions": ["SOC_USER"]
    }
}
```

### 4.2 获取用户信息
```
GET /api/v1/auth/userinfo
```

**响应数据：**
```json
{
    "id": 1,
    "userCode": "U001",
    "userName": "张三",
    "email": "<EMAIL>",
    "department": "技术部",
    "permissions": ["SOC_USER"],
    "productPermissions": ["产品A", "产品B"]
}
```

## 5. 任务管理接口

### 5.1 创建任务
```
POST /api/v1/tasks
```

**请求参数：**
```json
{
    "taskName": "string",
    "countryMto": "string",
    "mtoBranch": "string", 
    "customer": "string",
    "project": "string",
    "dataSource": "GBBS",
    "itemFile": "file"
}
```

**响应数据：**
```json
{
    "id": 1,
    "taskCode": "T202401010001",
    "taskName": "中国电信招标任务",
    "countryMto": "中国",
    "mtoBranch": "北京分公司",
    "customer": "中国电信",
    "project": "5G网络建设项目",
    "dataSource": "GBBS",
    "taskType": 1,
    "creatorId": 1,
    "createdTime": "2024-01-01T12:00:00Z"
}
```

### 5.2 查询任务列表
```
GET /api/v1/tasks
```

**查询参数：**
- `taskCode`: 任务编码（精确查询）
- `taskName`: 任务名称（模糊查询）
- `country`: 国家（精确查询）
- `customer`: 客户（精确查询）
- `project`: 项目（精确查询）
- `current`: 当前页码，默认1
- `pageSize`: 每页大小，默认20

**响应数据：**
```json
{
    "list": [
        {
            "id": 1,
            "taskCode": "T202401010001",
            "taskName": "中国电信招标任务",
            "countryMto": "中国",
            "customer": "中国电信",
            "project": "5G网络建设项目",
            "itemCount": 100,
            "answeredCount": 80,
            "answerProgress": "80/100",
            "totalSatisfaction": "85%",
            "creatorName": "张三",
            "createdTime": "2024-01-01T12:00:00Z",
            "updatedTime": "2024-01-01T15:00:00Z"
        }
    ],
    "pagination": {
        "current": 1,
        "pageSize": 20,
        "total": 50,
        "totalPages": 3
    }
}
```

### 5.3 获取任务详情
```
GET /api/v1/tasks/{taskId}
```

**响应数据：**
```json
{
    "id": 1,
    "taskCode": "T202401010001",
    "taskName": "中国电信招标任务",
    "countryMto": "中国",
    "mtoBranch": "北京分公司",
    "customer": "中国电信",
    "project": "5G网络建设项目",
    "dataSource": "GBBS",
    "taskType": 1,
    "creatorId": 1,
    "creatorName": "张三",
    "status": 1,
    "createdTime": "2024-01-01T12:00:00Z",
    "updatedTime": "2024-01-01T15:00:00Z"
}
```

### 5.4 更新任务
```
PUT /api/v1/tasks/{taskId}
```

**请求参数：**
```json
{
    "taskName": "string",
    "countryMto": "string",
    "mtoBranch": "string",
    "customer": "string", 
    "project": "string",
    "dataSource": "string"
}
```

### 5.5 复制任务
```
POST /api/v1/tasks/{taskId}/copy
```

**请求参数：**
```json
{
    "taskName": "string",
    "copyResponses": true
}
```

### 5.6 删除任务
```
DELETE /api/v1/tasks/{taskId}
```

## 6. 条目管理接口

### 6.1 查询条目列表
```
GET /api/v1/tasks/{taskId}/items
```

**查询参数：**
- `itemCode`: 条目编号（精确查询）
- `itemDescription`: 条目描述（模糊查询）
- `product`: 产品（精确查询）
- `responseStatus`: 应答状态（精确查询）
- `tags`: 标签（精确查询）
- `complianceLevel`: 应答（精确查询）
- `assignedTo`: 指派给（精确查询）
- `responseMethod`: 应答方式（精确查询）
- `responseSource`: 应答来源（精确查询）
- `current`: 当前页码，默认1
- `pageSize`: 每页大小，默认20

**响应数据：**
```json
{
    "list": [
        {
            "id": 1,
            "itemCode": "1.1",
            "itemDescription": "系统应支持高可用性",
            "tags": ["高可用", "系统要求"],
            "responses": [
                {
                    "id": 1,
                    "product": "产品A",
                    "responseStatus": 2,
                    "complianceLevel": "FC",
                    "responseContent": "系统支持99.9%可用性",
                    "responseMethod": "AI",
                    "responseSource": "GBBS",
                    "sourceIndex": "GBBS-001",
                    "assignedToName": "李四",
                    "updatedTime": "2024-01-01T15:00:00Z"
                }
            ],
            "createdTime": "2024-01-01T12:00:00Z"
        }
    ],
    "pagination": {
        "current": 1,
        "pageSize": 20,
        "total": 100,
        "totalPages": 5
    }
}
```

### 6.2 单条录入条目
```
POST /api/v1/tasks/{taskId}/items
```

**请求参数：**
```json
{
    "itemCode": "string",
    "itemDescription": "string",
    "product": "string",
    "tags": ["string"],
    "complianceLevel": "FC",
    "assignedTo": 1,
    "responseContent": "string",
    "supplementInfo": "string",
    "autoAnswer": true,
    "overwriteOnDuplicate": true,
    "remarks": "string"
}
```

### 6.3 批量导入条目
```
POST /api/v1/tasks/{taskId}/items/import
```

**请求参数：**
- `file`: Excel文件
- `autoAnswer`: 是否自动应答
- `overwriteOnDuplicate`: 重复时是否覆盖

**响应数据：**
```json
{
    "importResult": {
        "totalCount": 100,
        "successCount": 95,
        "failCount": 5,
        "duplicateCount": 3,
        "failedItems": [
            {
                "row": 10,
                "itemCode": "1.10",
                "error": "条目描述不能为空"
            }
        ]
    }
}
```

### 6.4 批量操作条目
```
POST /api/v1/tasks/{taskId}/items/batch
```

**请求参数：**
```json
{
    "action": "START_ANSWER|DELETE|ADD_TAGS|REMOVE_TAGS|SET_PRODUCT|ASSIGN",
    "itemIds": [1, 2, 3],
    "params": {
        "tags": ["标签1", "标签2"],
        "product": "产品A",
        "assignedTo": 1
    }
}
```

### 6.5 导出条目
```
GET /api/v1/tasks/{taskId}/items/export
```

**查询参数：**
- `products`: 产品列表，多个用逗号分隔
- `format`: 导出格式，默认excel

**响应：**
- 文件下载

## 7. 条目应答接口

### 7.1 获取条目应答详情
```
GET /api/v1/items/{itemId}/responses/{responseId}
```

**响应数据：**
```json
{
    "id": 1,
    "itemId": 1,
    "itemCode": "1.1",
    "itemDescription": "系统应支持高可用性",
    "product": "产品A",
    "responseStatus": 2,
    "complianceLevel": "FC",
    "responseContent": "系统支持99.9%可用性",
    "responseMethod": "AI",
    "responseSource": "GBBS",
    "sourceIndex": "GBBS-001",
    "matchScore": 95.5,
    "version": 1,
    "assignedToName": "李四",
    "createdTime": "2024-01-01T12:00:00Z",
    "updatedTime": "2024-01-01T15:00:00Z"
}
```

### 7.2 更新条目应答
```
PUT /api/v1/items/{itemId}/responses/{responseId}
```

**请求参数：**
```json
{
    "complianceLevel": "FC",
    "responseContent": "string",
    "sourceIndex": "string",
    "remarks": "string"
}
```

### 7.3 AI应答条目
```
POST /api/v1/items/{itemId}/ai-answer
```

**请求参数：**
```json
{
    "product": "string",
    "supplementInfo": "string"
}
```

**响应数据：**
```json
{
    "responseId": 1,
    "status": "processing",
    "message": "AI应答处理中"
}
```

### 7.4 获取AI匹配结果
```
GET /api/v1/items/{itemId}/ai-matches
```

**查询参数：**
- `product`: 产品
- `complianceLevel`: 满足度筛选
- `matchScore`: 匹配度筛选（≥90%，≥80%，≥70%）
- `dataSource`: 数据源筛选

**响应数据：**
```json
{
    "matches": [
        {
            "id": 1,
            "dataSource": "GBBS",
            "sourceItemId": "GBBS-001",
            "sourceDescription": "系统高可用性要求",
            "matchScore": 95.5,
            "countryMatch": true,
            "branchMatch": true,
            "customerMatch": false,
            "complianceLevel": "FC",
            "responseContent": "系统支持99.9%可用性",
            "sourceIndex": "GBBS-001-5.1",
            "isSelected": false
        }
    ],
    "summary": {
        "totalCount": 10,
        "fcCount": 6,
        "pcCount": 3,
        "ncCount": 1
    }
}
```

### 7.5 应用AI匹配结果
```
POST /api/v1/items/{itemId}/apply-match
```

**请求参数：**
```json
{
    "matchId": 1,
    "product": "string"
}
```

## 8. 数据分析接口

### 8.1 获取任务数据分析
```
GET /api/v1/tasks/{taskId}/analytics
```

**查询参数：**
- `assignedTo`: 指派给用户ID（可选，任务创建人可查看所有）

**响应数据：**
```json
{
    "overview": {
        "totalItems": 100,
        "answeredItems": 80,
        "unansweredItems": 15,
        "processingItems": 5,
        "answerRate": "80%",
        "fcCount": 50,
        "pcCount": 25,
        "ncCount": 5,
        "satisfaction": "85%"
    },
    "productAnalytics": [
        {
            "product": "产品A",
            "totalItems": 50,
            "answeredItems": 40,
            "fcCount": 25,
            "pcCount": 12,
            "ncCount": 3,
            "satisfaction": "88%"
        }
    ]
}
```

## 9. 快捷应答接口

### 9.1 快捷应答
```
POST /api/v1/quick-answer
```

**请求参数：**
```json
{
    "dataSource": "GBBS",
    "product": "string",
    "countryMto": "string",
    "mtoBranch": "string",
    "customer": "string",
    "itemDescription": "string",
    "supplementInfo": "string"
}
```

**响应数据：**
```json
{
    "taskId": 1,
    "itemId": 1,
    "responseId": 1,
    "redirectUrl": "/tasks/1/items"
}
```

## 10. 系统配置接口

### 10.1 获取数据源列表
```
GET /api/v1/config/data-sources
```

**响应数据：**
```json
{
    "dataSources": [
        {
            "code": "GBBS",
            "name": "GBBS系统",
            "type": "GBBS",
            "status": 1
        }
    ]
}
```

### 10.2 获取产品目录树
```
GET /api/v1/config/products
```

**查询参数：**
- `category`: 产品分类（SOC标准库、SOC积累库、产品目录树）
- `parentId`: 父级产品ID

**响应数据：**
```json
{
    "products": [
        {
            "id": 1,
            "productCode": "PROD_A",
            "productName": "产品A",
            "parentId": null,
            "level": 1,
            "category": "产品目录树",
            "children": [
                {
                    "id": 2,
                    "productCode": "PROD_A_1",
                    "productName": "产品A子模块1",
                    "parentId": 1,
                    "level": 2,
                    "category": "产品目录树"
                }
            ]
        }
    ]
}
```

### 10.3 获取标签列表
```
GET /api/v1/config/tags
```

**查询参数：**
- `keyword`: 标签名称关键字

**响应数据：**
```json
{
    "tags": [
        {
            "id": 1,
            "tagName": "高可用",
            "tagColor": "#1890ff",
            "usageCount": 10
        }
    ]
}
```

## 11. 文件管理接口

### 11.1 上传文件
```
POST /api/v1/files/upload
```

**请求参数：**
- `file`: 文件
- `relatedType`: 关联类型
- `relatedId`: 关联对象ID

**响应数据：**
```json
{
    "fileId": 1,
    "fileName": "条目导入模板.xlsx",
    "filePath": "/uploads/2024/01/01/xxx.xlsx",
    "fileSize": 1024,
    "fileType": "xlsx"
}
```

### 11.2 下载文件
```
GET /api/v1/files/{fileId}/download
```

### 11.3 下载模板
```
GET /api/v1/files/template/item-import
```

## 12. Agent交互接口

### 12.1 Agent对话
```
POST /api/v1/agent/chat
```

**请求参数：**
```json
{
    "message": "string",
    "sessionId": "string",
    "context": {
        "taskId": 1,
        "itemId": 1
    }
}
```

**响应数据：**
```json
{
    "sessionId": "string",
    "response": "string",
    "actions": [
        {
            "type": "CREATE_TASK",
            "params": {
                "taskName": "中国电信招标任务1"
            }
        }
    ],
    "suggestions": ["查看任务列表", "创建新任务"]
}
```

## 13. 错误码定义

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | success | 成功 |
| 400 | 参数错误 | 请求参数不正确 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限访问 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 409 | 资源冲突 | 资源已存在或状态冲突 |
| 500 | 服务器内部错误 | 系统内部错误 |

## 14. 接口安全

1. **认证机制**：使用JWT Token进行用户认证
2. **权限控制**：基于用户权限进行接口访问控制
3. **参数验证**：严格的参数校验和过滤
4. **频率限制**：接口调用频率限制
5. **日志记录**：完整的接口调用日志

## 15. 接口版本控制

- 使用URL路径版本控制：`/api/v1/`
- 向后兼容原则
- 废弃接口提前通知
