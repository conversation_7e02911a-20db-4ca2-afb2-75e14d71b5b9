# SOC智能应答系统 - 数据库表设计

## 1. 设计概述

### 1.1 设计原则
- **数据一致性**：通过外键约束保证数据的完整性和一致性
- **性能优化**：合理设计索引支持高频查询操作
- **扩展性**：预留扩展字段支持业务发展需要
- **安全性**：敏感数据加密存储，完整的审计日志记录
- **规范性**：统一的命名规范和数据类型标准

### 1.2 命名规范
- 表名：使用 `soc_` 前缀 + 下划线命名法，如 `soc_tasks`
- 字段名：下划线命名法，如 `task_name`、`create_time`
- 索引：`idx_表名_字段名`，如 `idx_tasks_creator_id`
- 外键：`fk_表名_关联表名`，如 `fk_items_task_id`

### 1.3 公共字段说明
所有业务表都包含以下公共字段：
- `id`：主键，BIGINT类型，自增
- `create_time`：创建时间，DATETIME类型，默认CURRENT_TIMESTAMP
- `update_time`：更新时间，DATETIME类型，默认CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
- `create_by`：创建人ID，BIGINT类型
- `update_by`：更新人ID，BIGINT类型
- `is_deleted`：逻辑删除标识，TINYINT类型，默认0（0:未删除, 1:已删除）

## 2. 核心业务表设计

### 2.1 用户管理相关表

#### 2.1.1 用户表 (soc_users)
```sql
CREATE TABLE `soc_users` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `real_name` VARCHAR(100) NOT NULL COMMENT '真实姓名',
  `employee_id` VARCHAR(20) NOT NULL COMMENT '工号',
  `email` VARCHAR(100) COMMENT '邮箱',
  `phone` VARCHAR(20) COMMENT '手机号',
  `department` VARCHAR(100) COMMENT '部门',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '用户状态(0:禁用, 1:启用)',
  `last_login_time` DATETIME COMMENT '最后登录时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_users_username` (`username`),
  UNIQUE KEY `uk_users_employee_id` (`employee_id`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_department` (`department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 2.1.2 用户产品权限表 (soc_user_permissions)
```sql
CREATE TABLE `soc_user_permissions` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `product_id` BIGINT NOT NULL COMMENT '产品ID',
  `permission_type` VARCHAR(20) NOT NULL DEFAULT 'VIEW' COMMENT '权限类型(VIEW:查看, EDIT:编辑, MANAGE:管理)',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_permissions` (`user_id`, `product_id`, `permission_type`),
  KEY `idx_user_permissions_user_id` (`user_id`),
  KEY `idx_user_permissions_product_id` (`product_id`),
  CONSTRAINT `fk_user_permissions_user_id` FOREIGN KEY (`user_id`) REFERENCES `soc_users` (`id`),
  CONSTRAINT `fk_user_permissions_product_id` FOREIGN KEY (`product_id`) REFERENCES `soc_products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户产品权限表';
```

### 2.2 任务管理相关表

#### 2.2.1 任务表 (soc_tasks)
```sql
CREATE TABLE `soc_tasks` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_code` VARCHAR(50) NOT NULL COMMENT '任务编码',
  `task_name` VARCHAR(200) NOT NULL COMMENT '任务名称',
  `country_mto` VARCHAR(100) COMMENT '国家/MTO',
  `mto_branch` VARCHAR(100) COMMENT 'MTO分支/省公司',
  `customer` VARCHAR(200) COMMENT '客户',
  `project` VARCHAR(200) COMMENT '项目',
  `data_source_id` BIGINT NOT NULL COMMENT '数据源ID',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '任务状态(ACTIVE:活跃, ARCHIVED:归档, DELETED:删除)',
  `total_items` INT NOT NULL DEFAULT 0 COMMENT '总条目数',
  `completed_items` INT NOT NULL DEFAULT 0 COMMENT '已完成条目数',
  `auto_answer_enabled` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用自动应答(0:否, 1:是)',
  `description` TEXT COMMENT '任务描述',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT NOT NULL COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tasks_task_code` (`task_code`),
  KEY `idx_tasks_task_name` (`task_name`),
  KEY `idx_tasks_creator_id` (`create_by`),
  KEY `idx_tasks_status` (`status`),
  KEY `idx_tasks_country_mto` (`country_mto`),
  KEY `idx_tasks_customer` (`customer`),
  KEY `idx_tasks_project` (`project`),
  CONSTRAINT `fk_tasks_creator_id` FOREIGN KEY (`create_by`) REFERENCES `soc_users` (`id`),
  CONSTRAINT `fk_tasks_data_source_id` FOREIGN KEY (`data_source_id`) REFERENCES `soc_data_sources` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOC任务表';
```

### 2.3 条目管理相关表

#### 2.3.1 条目表 (soc_items)
```sql
CREATE TABLE `soc_items` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '条目ID',
  `task_id` BIGINT NOT NULL COMMENT '所属任务ID',
  `item_code` VARCHAR(100) NOT NULL COMMENT '条目编号',
  `item_description` TEXT NOT NULL COMMENT '条目描述',
  `additional_info` TEXT COMMENT '补充信息',
  `status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '应答状态(PENDING:未应答, IN_PROGRESS:应答中, COMPLETED:已应答)',
  `assigned_to` BIGINT COMMENT '指派给用户ID',
  `priority` TINYINT NOT NULL DEFAULT 1 COMMENT '优先级(1:低, 2:中, 3:高)',
  `auto_answer_enabled` TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用自动应答(0:否, 1:是)',
  `overwrite_on_duplicate` TINYINT NOT NULL DEFAULT 1 COMMENT '重复时是否覆盖(0:否, 1:是)',
  `similarity_analyzed` TINYINT NOT NULL DEFAULT 0 COMMENT '是否已进行相似度分析(0:否, 1:是)',
  `remark` TEXT COMMENT '备注',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT NOT NULL COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_items_task_code` (`task_id`, `item_code`),
  KEY `idx_items_task_id` (`task_id`),
  KEY `idx_items_status` (`status`),
  KEY `idx_items_assigned_to` (`assigned_to`),
  KEY `idx_items_priority` (`priority`),
  KEY `idx_items_item_code` (`item_code`),
  CONSTRAINT `fk_items_task_id` FOREIGN KEY (`task_id`) REFERENCES `soc_tasks` (`id`),
  CONSTRAINT `fk_items_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `soc_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOC条目表';
```

#### 2.3.2 条目应答表 (soc_item_responses)
```sql
CREATE TABLE `soc_item_responses` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '应答ID',
  `item_id` BIGINT NOT NULL COMMENT '条目ID',
  `product_id` BIGINT NOT NULL COMMENT '产品ID',
  `compliance_level` VARCHAR(10) COMMENT '满足度(FC:完全满足, PC:部分满足, NC:不满足)',
  `response_content` LONGTEXT COMMENT '应答说明内容',
  `response_method` VARCHAR(20) NOT NULL DEFAULT 'MANUAL' COMMENT '应答方式(AI:AI应答, MANUAL:手工应答)',
  `data_source` VARCHAR(50) COMMENT '应答来源数据源',
  `source_index` VARCHAR(500) COMMENT '索引信息',
  `source_reference` TEXT COMMENT '来源引用',
  `match_score` DECIMAL(5,2) COMMENT 'AI匹配得分(0-100)',
  `version` INT NOT NULL DEFAULT 1 COMMENT '版本号',
  `is_current_version` TINYINT NOT NULL DEFAULT 1 COMMENT '是否当前版本(0:否, 1:是)',
  `additional_info` TEXT COMMENT '补充信息',
  `ai_enhanced` TINYINT NOT NULL DEFAULT 0 COMMENT '是否AI润色(0:否, 1:是)',
  `translated` TINYINT NOT NULL DEFAULT 0 COMMENT '是否已翻译(0:否, 1:是)',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT NOT NULL COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_responses` (`item_id`, `product_id`, `version`),
  KEY `idx_item_responses_item_id` (`item_id`),
  KEY `idx_item_responses_product_id` (`product_id`),
  KEY `idx_item_responses_compliance` (`compliance_level`),
  KEY `idx_item_responses_method` (`response_method`),
  KEY `idx_item_responses_current` (`is_current_version`),
  CONSTRAINT `fk_item_responses_item_id` FOREIGN KEY (`item_id`) REFERENCES `soc_items` (`id`),
  CONSTRAINT `fk_item_responses_product_id` FOREIGN KEY (`product_id`) REFERENCES `soc_products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目应答表';
```

### 2.4 AI应答相关表

#### 2.4.1 AI匹配结果表 (soc_ai_matches)
```sql
CREATE TABLE `soc_ai_matches` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '匹配结果ID',
  `item_id` BIGINT NOT NULL COMMENT '条目ID',
  `product_id` BIGINT NOT NULL COMMENT '产品ID',
  `data_source_id` BIGINT NOT NULL COMMENT '数据源ID',
  `source_item_id` VARCHAR(100) COMMENT '数据源中的条目ID',
  `source_item_description` TEXT COMMENT '数据源条目描述',
  `match_score` DECIMAL(5,2) NOT NULL COMMENT '匹配得分(0-100)',
  `country_match` TINYINT NOT NULL DEFAULT 0 COMMENT '国家匹配(0:不匹配, 1:匹配)',
  `branch_match` TINYINT NOT NULL DEFAULT 0 COMMENT '分支匹配(0:不匹配, 1:匹配)',
  `customer_match` TINYINT NOT NULL DEFAULT 0 COMMENT '客户匹配(0:不匹配, 1:匹配)',
  `compliance_level` VARCHAR(10) COMMENT '满足度',
  `response_content` LONGTEXT COMMENT '应答内容',
  `source_index` VARCHAR(500) COMMENT '索引信息',
  `is_applied` TINYINT NOT NULL DEFAULT 0 COMMENT '是否已应用(0:否, 1:是)',
  `rank_order` INT NOT NULL DEFAULT 0 COMMENT '排序序号',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_ai_matches_item_product` (`item_id`, `product_id`),
  KEY `idx_ai_matches_score` (`match_score` DESC),
  KEY `idx_ai_matches_data_source` (`data_source_id`),
  KEY `idx_ai_matches_rank` (`rank_order`),
  CONSTRAINT `fk_ai_matches_item_id` FOREIGN KEY (`item_id`) REFERENCES `soc_items` (`id`),
  CONSTRAINT `fk_ai_matches_product_id` FOREIGN KEY (`product_id`) REFERENCES `soc_products` (`id`),
  CONSTRAINT `fk_ai_matches_data_source_id` FOREIGN KEY (`data_source_id`) REFERENCES `soc_data_sources` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI匹配结果表';
```

### 2.5 标签管理相关表

#### 2.5.1 标签表 (soc_tags)
```sql
CREATE TABLE `soc_tags` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_name` VARCHAR(100) NOT NULL COMMENT '标签名称',
  `tag_color` VARCHAR(10) COMMENT '标签颜色',
  `tag_description` VARCHAR(500) COMMENT '标签描述',
  `usage_count` INT NOT NULL DEFAULT 0 COMMENT '使用次数',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tags_name` (`tag_name`),
  KEY `idx_tags_usage_count` (`usage_count` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';
```

#### 2.5.2 条目标签关联表 (soc_item_tags)
```sql
CREATE TABLE `soc_item_tags` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `item_id` BIGINT NOT NULL COMMENT '条目ID',
  `tag_id` BIGINT NOT NULL COMMENT '标签ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` BIGINT COMMENT '创建人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_tags` (`item_id`, `tag_id`),
  KEY `idx_item_tags_item_id` (`item_id`),
  KEY `idx_item_tags_tag_id` (`tag_id`),
  CONSTRAINT `fk_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `soc_items` (`id`),
  CONSTRAINT `fk_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `soc_tags` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目标签关联表';
```

### 2.6 系统配置相关表

#### 2.6.1 数据源配置表 (soc_data_sources)
```sql
CREATE TABLE `soc_data_sources` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '数据源ID',
  `source_name` VARCHAR(100) NOT NULL COMMENT '数据源名称',
  `source_type` VARCHAR(50) NOT NULL COMMENT '数据源类型(GBBS:GBBS系统, DOC_LIB:文档库, PROJECT_DOC:项目文档, HISTORY_SOC:历史SOC文档)',
  `source_url` VARCHAR(500) COMMENT '数据源地址',
  `config_json` JSON COMMENT '配置信息JSON',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态(0:禁用, 1:启用)',
  `description` TEXT COMMENT '描述',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_data_sources_name` (`source_name`),
  KEY `idx_data_sources_type` (`source_type`),
  KEY `idx_data_sources_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源配置表';
```

#### 2.6.2 产品表 (soc_products)
```sql
CREATE TABLE `soc_products` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `product_code` VARCHAR(50) NOT NULL COMMENT '产品编码',
  `product_name` VARCHAR(200) NOT NULL COMMENT '产品名称',
  `product_category` VARCHAR(100) COMMENT '产品分类',
  `parent_id` BIGINT COMMENT '父产品ID',
  `level` TINYINT NOT NULL DEFAULT 1 COMMENT '产品层级',
  `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序序号',
  `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态(0:禁用, 1:启用)',
  `description` TEXT COMMENT '产品描述',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_products_code` (`product_code`),
  KEY `idx_products_name` (`product_name`),
  KEY `idx_products_category` (`product_category`),
  KEY `idx_products_parent_id` (`parent_id`),
  KEY `idx_products_level` (`level`),
  KEY `idx_products_sort` (`sort_order`),
  CONSTRAINT `fk_products_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `soc_products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';
```

### 2.7 文件管理相关表

#### 2.7.1 文件表 (soc_files)
```sql
CREATE TABLE `soc_files` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `file_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_path` VARCHAR(500) NOT NULL COMMENT '文件存储路径',
  `file_size` BIGINT NOT NULL COMMENT '文件大小(字节)',
  `file_type` VARCHAR(50) NOT NULL COMMENT '文件类型',
  `file_extension` VARCHAR(10) COMMENT '文件扩展名',
  `mime_type` VARCHAR(100) COMMENT 'MIME类型',
  `business_type` VARCHAR(50) NOT NULL COMMENT '业务类型(TASK_IMPORT:任务导入, RESPONSE_IMAGE:应答图片)',
  `business_id` BIGINT COMMENT '关联业务ID',
  `md5_hash` VARCHAR(32) COMMENT '文件MD5值',
  `upload_status` TINYINT NOT NULL DEFAULT 1 COMMENT '上传状态(0:失败, 1:成功)',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` BIGINT NOT NULL COMMENT '创建人ID',
  `update_by` BIGINT COMMENT '更新人ID',
  `is_deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除, 1:已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_files_business` (`business_type`, `business_id`),
  KEY `idx_files_creator` (`create_by`),
  KEY `idx_files_type` (`file_type`),
  KEY `idx_files_md5` (`md5_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件表';
```

### 2.8 系统日志相关表

#### 2.8.1 操作日志表 (soc_operation_logs)
```sql
CREATE TABLE `soc_operation_logs` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` BIGINT NOT NULL COMMENT '操作用户ID',
  `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型',
  `business_type` VARCHAR(50) NOT NULL COMMENT '业务类型',
  `business_id` BIGINT COMMENT '业务对象ID',
  `operation_desc` VARCHAR(500) NOT NULL COMMENT '操作描述',
  `request_method` VARCHAR(10) COMMENT '请求方法',
  `request_url` VARCHAR(500) COMMENT '请求URL',
  `request_params` TEXT COMMENT '请求参数',
  `response_result` TEXT COMMENT '响应结果',
  `ip_address` VARCHAR(50) COMMENT 'IP地址',
  `user_agent` VARCHAR(500) COMMENT '用户代理',
  `operation_status` TINYINT NOT NULL DEFAULT 1 COMMENT '操作状态(0:失败, 1:成功)',
  `error_message` TEXT COMMENT '错误信息',
  `execution_time` INT COMMENT '执行耗时(毫秒)',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_operation_logs_user_id` (`user_id`),
  KEY `idx_operation_logs_type` (`operation_type`),
  KEY `idx_operation_logs_business` (`business_type`, `business_id`),
  KEY `idx_operation_logs_time` (`create_time`),
  KEY `idx_operation_logs_status` (`operation_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

## 3. 表关系说明

### 3.1 主要实体关系
- **用户 → 任务**：一对多关系，一个用户可以创建多个任务
- **任务 → 条目**：一对多关系，一个任务包含多个条目
- **条目 → 应答**：一对多关系，一个条目可以有多个产品维度的应答
- **条目 → 标签**：多对多关系，通过中间表关联
- **条目 → AI匹配结果**：一对多关系，一个条目可以有多个匹配结果
- **用户 → 产品权限**：多对多关系，用户可以申请多个产品权限

### 3.2 外键约束关系
```
soc_tasks.create_by → soc_users.id
soc_tasks.data_source_id → soc_data_sources.id
soc_items.task_id → soc_tasks.id
soc_items.assigned_to → soc_users.id
soc_item_responses.item_id → soc_items.id
soc_item_responses.product_id → soc_products.id
soc_item_tags.item_id → soc_items.id
soc_item_tags.tag_id → soc_tags.id
soc_ai_matches.item_id → soc_items.id
soc_ai_matches.product_id → soc_products.id
soc_ai_matches.data_source_id → soc_data_sources.id
soc_user_permissions.user_id → soc_users.id
soc_user_permissions.product_id → soc_products.id
soc_products.parent_id → soc_products.id
```

## 4. 索引设计说明

### 4.1 主要查询场景及索引
1. **任务列表查询**：按创建人、任务名称、国家、客户、项目等条件查询
   - `idx_tasks_creator_id`、`idx_tasks_task_name`、`idx_tasks_country_mto`等
   
2. **条目列表查询**：按任务、状态、指派人、产品等条件查询
   - `idx_items_task_id`、`idx_items_status`、`idx_items_assigned_to`等
   
3. **应答结果查询**：按条目、产品、满足度等条件查询
   - `idx_item_responses_item_id`、`idx_item_responses_product_id`等
   
4. **AI匹配结果查询**：按匹配度排序、数据源筛选等
   - `idx_ai_matches_score`、`idx_ai_matches_data_source`等

### 4.2 复合索引优化
- `uk_items_task_code`：任务+条目编码唯一约束
- `uk_item_responses`：条目+产品+版本唯一约束
- `idx_ai_matches_item_product`：条目+产品复合查询

## 5. 数据字典

### 5.1 枚举值说明

#### 应答状态 (status)
- `PENDING`：未应答
- `IN_PROGRESS`：应答中
- `COMPLETED`：已应答

#### 满足度 (compliance_level)
- `FC`：Full Compliance，完全满足
- `PC`：Partially Compliance，部分满足
- `NC`：Not Compliance，不满足

#### 应答方式 (response_method)
- `AI`：AI应答
- `MANUAL`：手工应答

#### 数据源类型 (source_type)
- `GBBS`：GBBS系统
- `DOC_LIB`：文档库
- `PROJECT_DOC`：项目文档
- `HISTORY_SOC`：历史SOC文档

### 5.2 业务规则约束
1. **唯一性约束**
   - 任务编码在全局范围内唯一
   - 条目编码在同一任务内唯一
   - 条目+产品+版本组合唯一

2. **完整性约束**
   - 任务必须指定创建人和数据源
   - 条目必须关联有效任务
   - 应答必须关联有效条目和产品

3. **业务逻辑约束**
   - 应答中状态的条目不允许修改基本信息
   - 只有当前版本的应答参与统计分析
   - 逻辑删除的数据不参与业务查询

## 6. 性能优化建议

### 6.1 查询优化
- 合理使用分页查询，避免全表扫描
- 对高频查询字段建立适当索引
- 使用覆盖索引减少回表查询
- 定期分析慢查询并优化

### 6.2 存储优化
- 合理设置字段长度，避免空间浪费
- 大文本字段考虑分离存储
- 定期清理历史数据和日志
- 考虑数据分区策略

### 6.3 维护建议
- 定期更新表统计信息
- 监控索引使用情况
- 定期检查约束完整性
- 建立数据备份和恢复机制

## 7. 扩展性考虑

### 7.1 水平扩展
- 支持按任务或用户维度分库分表
- 预留分片键字段
- 考虑读写分离架构

### 7.2 功能扩展
- 预留扩展字段支持新功能
- 支持多数据源接入
- 支持自定义字段和属性
- 支持工作流引擎集成