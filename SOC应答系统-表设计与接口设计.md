# SOC智能应答系统 - 表设计与接口设计

## 一、数据库表设计

### 1. 任务表（soc_task）
```sql
CREATE TABLE soc_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    task_code VARCHAR(50) NOT NULL UNIQUE COMMENT '任务编码，格式：TASK001',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    country VARCHAR(100) COMMENT '国家/MTO',
    mto_branch VARCHAR(100) COMMENT 'MTO分支',
    customer VARCHAR(200) COMMENT '客户',
    project VARCHAR(200) COMMENT '项目',
    data_source VARCHAR(50) DEFAULT 'GBBS' COMMENT '数据源：GBBS,文档库,项目文档,历史SOC文档',
    attachment_file_path VARCHAR(500) COMMENT '应答条目文件路径',
    status VARCHAR(20) DEFAULT '未开始' COMMENT '任务状态：未开始,进行中,已完成',
    is_personal TINYINT(1) DEFAULT 0 COMMENT '是否为个人任务：0-否,1-是',
    create_user VARCHAR(100) NOT NULL COMMENT '创建人（姓名+工号）',
    create_user_id VARCHAR(50) NOT NULL COMMENT '创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user VARCHAR(100) COMMENT '更新人（姓名+工号）',
    update_user_id VARCHAR(50) COMMENT '更新人ID', 
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否,1-是',
    INDEX idx_task_code (task_code),
    INDEX idx_create_user_id (create_user_id),
    INDEX idx_country (country),
    INDEX idx_customer (customer),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOC应答任务表';
```

### 2. 条目表（soc_item）
```sql
CREATE TABLE soc_item (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    item_code VARCHAR(100) NOT NULL COMMENT '条目编号',
    description TEXT NOT NULL COMMENT '条目描述',
    additional_info TEXT COMMENT '补充信息',
    assign_user VARCHAR(100) COMMENT '指派给（姓名+工号）',
    assign_user_id VARCHAR(50) COMMENT '指派给用户ID',
    status VARCHAR(20) DEFAULT '未应答' COMMENT '应答状态：未应答,应答中,已应答',
    auto_response TINYINT(1) DEFAULT 1 COMMENT '是否自动应答：0-否,1-是',
    overwrite_when_duplicate TINYINT(1) DEFAULT 1 COMMENT '重复时是否覆盖：0-否,1-是',
    remark TEXT COMMENT '备注',
    create_user VARCHAR(100) NOT NULL COMMENT '创建人（姓名+工号）',
    create_user_id VARCHAR(50) NOT NULL COMMENT '创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user VARCHAR(100) COMMENT '更新人（姓名+工号）',
    update_user_id VARCHAR(50) COMMENT '更新人ID',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否,1-是',
    INDEX idx_task_id (task_id),
    INDEX idx_item_code (item_code),
    INDEX idx_assign_user_id (assign_user_id),
    INDEX idx_status (status),
    UNIQUE KEY uk_task_item_code (task_id, item_code, is_deleted),
    FOREIGN KEY (task_id) REFERENCES soc_task(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='SOC应答条目表';
```

### 3. 条目产品关联表（soc_item_product）
```sql
CREATE TABLE soc_item_product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    product VARCHAR(200) NOT NULL COMMENT '产品名称',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_method VARCHAR(20) COMMENT '应答方式：AI,手工',
    response_content LONGTEXT COMMENT '应答说明（支持富文本）',
    source VARCHAR(50) COMMENT '应答来源：GBBS,文档库等',
    source_index VARCHAR(200) COMMENT '索引链接',
    match_score DECIMAL(5,2) COMMENT '匹配度分数',
    status VARCHAR(20) DEFAULT '未应答' COMMENT '应答状态：未应答,应答中,已应答',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user VARCHAR(100) COMMENT '更新人（姓名+工号）',
    update_user_id VARCHAR(50) COMMENT '更新人ID',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除：0-否,1-是',
    INDEX idx_item_id (item_id),
    INDEX idx_task_id (task_id),
    INDEX idx_product (product),
    INDEX idx_satisfaction (satisfaction),
    INDEX idx_status (status),
    UNIQUE KEY uk_item_product (item_id, product, is_deleted),
    FOREIGN KEY (item_id) REFERENCES soc_item(id),
    FOREIGN KEY (task_id) REFERENCES soc_task(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目产品关联表';
```

### 4. 标签表（soc_tag）
```sql
CREATE TABLE soc_tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    create_user_id VARCHAR(50) NOT NULL COMMENT '创建人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_tag_name (tag_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签字典表';
```

### 5. 条目标签关联表（soc_item_tag）
```sql
CREATE TABLE soc_item_tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称（冗余字段）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_item_id (item_id),
    INDEX idx_tag_id (tag_id),
    UNIQUE KEY uk_item_tag (item_id, tag_id),
    FOREIGN KEY (item_id) REFERENCES soc_item(id),
    FOREIGN KEY (tag_id) REFERENCES soc_tag(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目标签关联表';
```

### 6. AI匹配结果表（soc_ai_match_result）
```sql
CREATE TABLE soc_ai_match_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配结果ID',
    item_product_id BIGINT NOT NULL COMMENT '条目产品关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源：GBBS,文档库等',
    source_id VARCHAR(100) COMMENT '数据源中的记录ID',
    source_description TEXT COMMENT '数据源条目描述',
    match_score DECIMAL(5,2) NOT NULL COMMENT '匹配度分数',
    country_match TINYINT(1) DEFAULT 0 COMMENT '国家匹配：0-否,1-是',
    branch_match TINYINT(1) DEFAULT 0 COMMENT '分支匹配：0-否,1-是', 
    customer_match TINYINT(1) DEFAULT 0 COMMENT '客户匹配：0-否,1-是',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_content LONGTEXT COMMENT '应答说明',
    source_index VARCHAR(200) COMMENT '索引链接',
    is_applied TINYINT(1) DEFAULT 0 COMMENT '是否已应用：0-否,1-是',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_item_product_id (item_product_id),
    INDEX idx_item_id (item_id),
    INDEX idx_task_id (task_id),
    INDEX idx_match_score (match_score),
    INDEX idx_data_source (data_source),
    FOREIGN KEY (item_product_id) REFERENCES soc_item_product(id),
    FOREIGN KEY (item_id) REFERENCES soc_item(id),
    FOREIGN KEY (task_id) REFERENCES soc_task(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI匹配结果表';
```

### 7. 历史版本表（soc_item_history）
```sql
CREATE TABLE soc_item_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '历史版本ID',
    item_product_id BIGINT NOT NULL COMMENT '条目产品关联ID',
    version_num INT NOT NULL COMMENT '版本号',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_method VARCHAR(20) COMMENT '应答方式：AI,手工',
    response_content LONGTEXT COMMENT '应答说明',
    source VARCHAR(50) COMMENT '应答来源',
    source_index VARCHAR(200) COMMENT '索引链接',
    additional_info TEXT COMMENT '补充信息',
    remark TEXT COMMENT '备注',
    change_type VARCHAR(20) NOT NULL COMMENT '变更类型：create,update,ai_enhance,manual_edit',
    change_description VARCHAR(500) COMMENT '变更说明',
    create_user VARCHAR(100) NOT NULL COMMENT '操作人（姓名+工号）',
    create_user_id VARCHAR(50) NOT NULL COMMENT '操作人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_item_product_id (item_product_id),
    INDEX idx_version_num (version_num),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (item_product_id) REFERENCES soc_item_product(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条目历史版本表';
```

### 8. 用户权限表（soc_user_permission）
```sql
CREATE TABLE soc_user_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    user_name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    role VARCHAR(50) NOT NULL COMMENT '角色：SOC智能应答-普通用户',
    product_permissions JSON COMMENT '产品权限列表（JSON格式）',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活：0-否,1-是',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_role (role)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户权限表';
```

### 9. 操作日志表（soc_operation_log）
```sql
CREATE TABLE soc_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id VARCHAR(50) NOT NULL COMMENT '操作人ID',
    user_name VARCHAR(100) NOT NULL COMMENT '操作人姓名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(500) NOT NULL COMMENT '操作描述',
    target_type VARCHAR(50) COMMENT '操作对象类型：task,item,item_product',
    target_id BIGINT COMMENT '操作对象ID',
    before_data JSON COMMENT '操作前数据',
    after_data JSON COMMENT '操作后数据',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_target_type_id (target_type, target_id),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

## 二、接口设计

### 1. 任务管理接口

#### 1.1 创建任务
```
POST /api/soc/tasks
Content-Type: application/json

Request Body:
{
    "taskName": "华为云Stack解决方案技术标",
    "country": "中国",
    "mtoBranch": "华为技术有限公司", 
    "customer": "某银行",
    "project": "云计算平台建设项目",
    "dataSource": "GBBS",
    "attachmentFile": "multipart/form-data file"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "taskId": 1,
        "taskCode": "TASK001"
    }
}
```

#### 1.2 查询任务列表
```
GET /api/soc/tasks?page=1&size=20&taskCode=&taskName=&country=&customer=&project=

Response:
{
    "code": 200,
    "message": "success", 
    "data": {
        "total": 100,
        "list": [
            {
                "id": 1,
                "taskCode": "TASK001",
                "taskName": "华为云Stack解决方案技术标",
                "country": "中国",
                "customer": "某银行",
                "project": "云计算平台建设项目",
                "itemCount": 25,
                "completedCount": 20,
                "satisfaction": 85,
                "status": "进行中",
                "isPersonal": false,
                "createTime": "2024-01-10 09:00:00",
                "updateTime": "2024-01-15 16:30:00"
            }
        ]
    }
}
```

#### 1.3 获取任务详情
```
GET /api/soc/tasks/{taskId}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "taskCode": "TASK001",
        "taskName": "华为云Stack解决方案技术标",
        "country": "中国",
        "mtoBranch": "华为技术有限公司",
        "customer": "某银行", 
        "project": "云计算平台建设项目",
        "dataSource": "GBBS",
        "status": "进行中",
        "isPersonal": false,
        "createUser": "张三（123456）",
        "createTime": "2024-01-10 09:00:00",
        "updateTime": "2024-01-15 16:30:00"
    }
}
```

#### 1.4 更新任务
```
PUT /api/soc/tasks/{taskId}
Content-Type: application/json

Request Body:
{
    "taskName": "华为云Stack解决方案技术标-更新",
    "country": "中国",
    "mtoBranch": "华为技术有限公司",
    "customer": "某银行",
    "project": "云计算平台建设项目",
    "dataSource": "GBBS"
}

Response:
{
    "code": 200,
    "message": "success"
}
```

#### 1.5 复制任务
```
POST /api/soc/tasks/{taskId}/copy
Content-Type: application/json

Request Body:
{
    "taskName": "华为云Stack解决方案技术标_复制",
    "country": "中国",
    "mtoBranch": "华为技术有限公司", 
    "customer": "某银行",
    "project": "云计算平台建设项目",
    "copyItemResults": true,
    "attachmentFile": "multipart/form-data file (optional)"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "taskId": 2,
        "taskCode": "TASK002"
    }
}
```

#### 1.6 删除任务
```
DELETE /api/soc/tasks/{taskId}

Response:
{
    "code": 200,
    "message": "success"
}
```

### 2. 条目管理接口

#### 2.1 查询条目列表
```
GET /api/soc/tasks/{taskId}/items?page=1&size=20&code=&description=&product=&status=&tag=&satisfaction=&assignee=&responseMethod=&source=

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 50,
        "list": [
            {
                "id": 1,
                "itemCode": "CODE001",
                "description": "云平台基础架构能力要求...",
                "products": [
                    {
                        "id": 1,
                        "product": "华为云Stack",
                        "status": "已应答",
                        "satisfaction": "FC",
                        "responseMethod": "AI应答",
                        "responseContent": "华为云Stack提供...",
                        "source": "GBBS",
                        "sourceIndex": "GBBS-001",
                        "updateUser": "张三（123456）",
                        "updateTime": "2024-01-15 10:30"
                    }
                ],
                "tags": ["基础架构", "云平台"],
                "assignUser": "张三（123456）",
                "remark": "",
                "createTime": "2024-01-10 09:00:00"
            }
        ]
    }
}
```

#### 2.2 单条录入条目
```
POST /api/soc/tasks/{taskId}/items
Content-Type: application/json

Request Body:
{
    "itemCode": "CODE001",
    "description": "云平台基础架构能力要求，需要支持虚拟机、容器、存储等资源的统一管理",
    "product": "华为云Stack",
    "tags": ["基础架构", "云平台"],
    "satisfaction": "FC",
    "assignUser": "张三（123456）",
    "responseContent": "华为云Stack提供统一的云平台基础架构...",
    "additionalInfo": "补充信息",
    "autoResponse": true,
    "overwriteWhenDuplicate": true,
    "remark": "备注信息"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "itemId": 1,
        "itemProductId": 1
    }
}
```

#### 2.3 批量导入条目
```
POST /api/soc/tasks/{taskId}/items/batch-import
Content-Type: multipart/form-data

Request:
- file: Excel文件
- autoResponse: true
- overwriteWhenDuplicate: true

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "successCount": 20,
        "failureCount": 2,
        "failures": [
            {
                "row": 5,
                "itemCode": "CODE005",
                "error": "条目CODE005已存在，请修改后再试！"
            }
        ]
    }
}
```

#### 2.4 开始应答（批量AI应答）
```
POST /api/soc/tasks/{taskId}/items/start-response
Content-Type: application/json

Request Body:
{
    "itemIds": [1, 2, 3], // 空数组表示全部条目
    "products": ["华为云Stack", "FusionSphere"] // 可选，指定产品
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "jobId": "job_20240115_001",
        "totalCount": 25
    }
}
```

#### 2.5 单个AI应答
```
POST /api/soc/items/{itemId}/products/{productId}/ai-response

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "jobId": "job_20240115_002"
    }
}
```

#### 2.6 手工应答保存
```
PUT /api/soc/items/{itemId}/products/{productId}/manual-response
Content-Type: application/json

Request Body:
{
    "satisfaction": "FC",
    "responseContent": "华为云Stack提供统一的云平台基础架构管理能力...",
    "additionalInfo": "补充信息",
    "remark": "备注",
    "sourceIndex": "GBBS-001"
}

Response:
{
    "code": 200,
    "message": "success"
}
```

#### 2.7 批量操作

##### 2.7.1 批量删除
```
DELETE /api/soc/tasks/{taskId}/items/batch
Content-Type: application/json

Request Body:
{
    "itemIds": [1, 2, 3] // 空数组表示全部条目
}

Response:
{
    "code": 200,
    "message": "success"
}
```

##### 2.7.2 批量添加标签
```
POST /api/soc/tasks/{taskId}/items/batch-add-tags
Content-Type: application/json

Request Body:
{
    "itemIds": [1, 2, 3], // 空数组表示全部条目
    "tags": ["新标签1", "新标签2"]
}

Response:
{
    "code": 200,
    "message": "success"
}
```

##### 2.7.3 批量移除标签
```
DELETE /api/soc/tasks/{taskId}/items/batch-remove-tags
Content-Type: application/json

Request Body:
{
    "itemIds": [1, 2, 3], // 空数组表示全部条目
    "tags": ["要删除的标签1", "要删除的标签2"]
}

Response:
{
    "code": 200,
    "message": "success"
}
```

##### 2.7.4 批量设置产品
```
PUT /api/soc/tasks/{taskId}/items/batch-set-product
Content-Type: application/json

Request Body:
{
    "itemIds": [1, 2, 3], // 空数组表示全部条目
    "product": "华为云Stack"
}

Response:
{
    "code": 200,
    "message": "success"
}
```

##### 2.7.5 批量指派
```
PUT /api/soc/tasks/{taskId}/items/batch-assign
Content-Type: application/json

Request Body:
{
    "itemIds": [1, 2, 3], // 空数组表示全部条目
    "assignUser": "李四（789012）"
}

Response:
{
    "code": 200,
    "message": "success"
}
```

#### 2.8 导出条目
```
POST /api/soc/tasks/{taskId}/items/export
Content-Type: application/json

Request Body:
{
    "products": ["华为云Stack", "FusionSphere"], // 空数组表示全部产品
    "itemIds": [1, 2, 3] // 可选，指定条目ID
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "downloadUrl": "https://xxx.com/download/task_001_export.xlsx",
        "fileName": "华为云Stack解决方案技术标_导出_20240115.xlsx"
    }
}
```

### 3. 数据分析接口

#### 3.1 获取任务分析数据
```
GET /api/soc/tasks/{taskId}/analysis?assignee=

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "overview": {
            "totalCount": 25,
            "completedCount": 20,
            "pendingCount": 3,
            "processingCount": 2,
            "completionRate": 80,
            "fcCount": 15,
            "pcCount": 4,
            "ncCount": 1,
            "satisfactionRate": 85
        },
        "productStats": [
            {
                "product": "华为云Stack",
                "totalCount": 15,
                "completedCount": 12,
                "fcCount": 10,
                "pcCount": 2,
                "ncCount": 0,
                "satisfactionRate": 90
            }
        ]
    }
}
```

### 4. AI匹配相关接口

#### 4.1 获取AI匹配详情
```
GET /api/soc/items/{itemId}/products/{productId}/ai-matches?matchScore=&satisfaction=&dataSource=

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "dataSources": [
            {
                "dataSource": "GBBS",
                "totalCount": 15,
                "fcCount": 8,
                "pcCount": 5,
                "ncCount": 2,
                "matches": [
                    {
                        "id": 1,
                        "matchScore": 95.5,
                        "countryMatch": true,
                        "branchMatch": true,
                        "customerMatch": false,
                        "sourceDescription": "云平台基础架构统一管理能力...",
                        "satisfaction": "FC",
                        "responseContent": "华为云Stack基于OpenStack架构...",
                        "sourceIndex": "GBBS-HCS-001",
                        "isApplied": false
                    }
                ]
            }
        ]
    }
}
```

#### 4.2 应用AI匹配结果
```
POST /api/soc/items/{itemId}/products/{productId}/apply-ai-match
Content-Type: application/json

Request Body:
{
    "matchResultId": 1
}

Response:
{
    "code": 200,
    "message": "success"
}
```

### 5. 快捷应答接口

#### 5.1 快捷应答
```
POST /api/soc/quick-response
Content-Type: application/json

Request Body:
{
    "product": "华为云Stack",
    "country": "中国",
    "mtoBranch": "华为技术有限公司",
    "customer": "某银行",
    "description": "云平台基础架构能力要求，需要支持虚拟机、容器、存储等资源的统一管理"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "taskId": 100, // 自动创建的个人任务ID
        "itemId": 200, // 创建的条目ID
        "jobId": "job_20240115_003" // AI应答任务ID
    }
}
```

### 6. 辅助接口

#### 6.1 获取产品列表
```
GET /api/soc/products

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "name": "华为云Stack",
            "category": "SOC标准库",
            "parentId": null
        },
        {
            "id": 2,
            "name": "FusionSphere",
            "category": "SOC积累库", 
            "parentId": null
        }
    ]
}
```

#### 6.2 获取标签列表
```
GET /api/soc/tags?keyword=

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "tagName": "基础架构"
        },
        {
            "id": 2,
            "tagName": "云平台"
        }
    ]
}
```

#### 6.3 获取用户列表
```
GET /api/soc/users?keyword=

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "userId": "123456",
            "userName": "张三（123456）",
            "department": "云服务产品部"
        }
    ]
}
```

#### 6.4 下载模板
```
GET /api/soc/templates/download?type=import

Response:
File download (Excel template)
```

#### 6.5 获取历史版本
```
GET /api/soc/items/{itemId}/products/{productId}/history

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "versionNum": 1,
            "satisfaction": "PC",
            "responseMethod": "AI应答",
            "responseContent": "初始AI应答内容...",
            "changeType": "create",
            "changeDescription": "AI自动生成",
            "createUser": "系统",
            "createTime": "2024-01-15 10:30:00"
        },
        {
            "id": 2,
            "versionNum": 2,
            "satisfaction": "FC",
            "responseMethod": "手工应答",
            "responseContent": "手工修改后的内容...",
            "changeType": "manual_edit",
            "changeDescription": "手工修改满足度和内容",
            "createUser": "张三（123456）",
            "createTime": "2024-01-15 11:15:00"
        }
    ]
}
```

### 7. AI增强功能接口

#### 7.1 AI润色
```
POST /api/soc/ai/polish
Content-Type: application/json

Request Body:
{
    "content": "原始应答说明内容",
    "language": "zh" // zh-中文, en-英文
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "polishedContent": "润色后的内容",
        "suggestions": [
            "建议1：增加具体技术细节",
            "建议2：完善性能指标说明"
        ]
    }
}
```

#### 7.2 AI翻译
```
POST /api/soc/ai/translate
Content-Type: application/json

Request Body:
{
    "content": "需要翻译的内容",
    "targetLanguage": "en" // en-英文, zh-中文
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "translatedContent": "Translated content...",
        "sourceLanguage": "zh"
    }
}
```

#### 7.3 AI应答任务状态查询
```
GET /api/soc/ai/jobs/{jobId}/status

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "jobId": "job_20240115_001",
        "status": "processing", // pending, processing, completed, failed
        "progress": 75,
        "totalCount": 25,
        "completedCount": 18,
        "failedCount": 1,
        "startTime": "2024-01-15 14:00:00",
        "estimatedEndTime": "2024-01-15 14:05:00",
        "errorMessages": [
            "条目CODE005匹配失败：数据源无相关数据"
        ]
    }
}
```

## 三、统一返回格式

### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {...},
    "timestamp": "2024-01-15T14:30:00Z"
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "参数错误",
    "error": "字段validation详细错误信息",
    "timestamp": "2024-01-15T14:30:00Z"
}
```

### 常见错误码
- 200: 成功
- 400: 请求参数错误
- 401: 未登录
- 403: 无权限访问
- 404: 资源不存在
- 500: 服务器内部错误
- 1001: 任务不存在
- 1002: 条目不存在
- 1003: 权限不足
- 1004: 文件格式错误
- 1005: AI服务不可用

## 四、技术实现要点

### 1. 数据库优化
- 使用InnoDB引擎支持事务
- 合理设置索引提高查询性能
- JSON字段存储灵活配置信息
- 软删除保证数据安全

### 2. 缓存策略
- Redis缓存用户权限信息
- 缓存产品目录树结构
- 缓存热门标签列表
- 分页查询结果缓存

### 3. 异步处理
- AI应答任务异步处理
- 批量导入异步处理
- 文件导出异步处理
- WebSocket推送进度更新

### 4. 安全考虑
- 接口权限控制
- 数据权限隔离
- 文件上传安全检查
- SQL注入防护

### 5. 性能优化
- 分页查询优化
- 批量操作优化
- 大文件处理优化
- 数据库连接池配置

### 6. 监控告警
- 接口响应时间监控
- AI服务可用性监控
- 数据库性能监控
- 业务指标监控

## 五、Agent交互接口设计

### 1. Agent对话接口

#### 1.1 Agent聊天对话
```
POST /api/soc/agent/chat
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "message": "帮我创建一个中国电信的招标任务",
    "conversationId": "conv_20240115_001", // 可选，维持对话上下文
    "context": {
        "currentTaskId": 1, // 可选，当前操作的任务ID
        "currentItemId": 10 // 可选，当前操作的条目ID
    }
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "reply": "好的，我来帮您创建中国电信的招标任务。请提供以下信息：\n1. 任务名称\n2. MTO分支\n3. 项目名称\n4. 是否需要上传条目文件？",
        "conversationId": "conv_20240115_001",
        "nextActions": [
            {
                "action": "create_task",
                "label": "创建任务",
                "parameters": {
                    "customer": "中国电信"
                }
            }
        ],
        "toolCalls": [] // 如果Agent需要调用工具，会在这里返回
    }
}
```

#### 1.2 Agent工具调用状态查询
```
GET /api/soc/agent/tool-calls/{callId}/status

Response:
{
    "code": 200,
    "message": "success", 
    "data": {
        "callId": "call_20240115_001",
        "status": "completed", // pending, running, completed, failed
        "result": {
            "taskId": 123,
            "taskCode": "TASK001",
            "message": "任务创建成功"
        },
        "error": null
    }
}
```

### 2. Agent工具接口（Tools）

#### 2.1 创建任务工具
```
POST /api/soc/agent/tools/create-task
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "taskName": "中国电信5G基站建设项目SOC应答",
    "country": "中国", 
    "mtoBranch": "北京分公司",
    "customer": "中国电信",
    "projectName": "5G基站建设项目",
    "dataSource": "GBBS"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "taskId": 123,
        "taskCode": "TASK001",
        "callId": "call_20240115_001"
    }
}
```

#### 2.2 查询任务工具
```
POST /api/soc/agent/tools/query-tasks
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "filters": {
        "taskName": "电信",
        "customer": "中国电信",
        "assignedToMe": true // 查询指派给我的任务
    },
    "limit": 10
}

Response:
{
    "code": 200,  
    "message": "success",
    "data": {
        "tasks": [
            {
                "id": 123,
                "taskCode": "TASK001",
                "taskName": "中国电信5G基站建设项目SOC应答",
                "status": "进行中",
                "itemCount": 50,
                "completedCount": 30,
                "assignedItemCount": 10
            }
        ],
        "totalCount": 5
    }
}
```

#### 2.3 查询条目工具
```
POST /api/soc/agent/tools/query-items
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "taskId": 123, // 可选，不提供则查询用户相关的所有条目
    "filters": {
        "itemDescription": "网络安全",
        "status": "未应答",
        "assignedToMe": true
    },
    "limit": 10
}

Response:
{
    "code": 200,
    "message": "success", 
    "data": {
        "items": [
            {
                "id": 456,
                "itemCode": "CODE001",
                "description": "网络安全防护能力要求",
                "taskName": "中国电信5G基站建设项目SOC应答",
                "status": "未应答",
                "assignedUser": "张三（123456）"
            }
        ],
        "totalCount": 8
    }
}
```

#### 2.4 条目应答工具
```
POST /api/soc/agent/tools/answer-item
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "itemId": 456, // 可选，如果不提供则根据描述创建新条目
    "taskId": 123, // itemId不存在时必填
    "itemDescription": "网络安全防护能力要求", // itemId不存在时必填
    "product": "华为防火墙",
    "autoAnswer": true, // 是否触发AI应答
    "responseContent": "华为防火墙提供完整的网络安全防护..." // 手动应答内容
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "itemId": 456,
        "itemProductId": 789,
        "aiJobId": "job_20240115_001", // 如果触发了AI应答
        "message": "条目应答成功"
    }
}
```

#### 2.5 批量应答工具
```
POST /api/soc/agent/tools/batch-answer
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "taskId": 123,
    "filters": {
        "status": "未应答",
        "assignedToMe": true,
        "products": ["华为防火墙", "华为交换机"]
    }
}

Response:
{
    "code": 200, 
    "message": "success",
    "data": {
        "jobId": "job_20240115_002",
        "totalCount": 15,
        "message": "批量应答任务已启动"
    }
}
```

#### 2.6 Excel导入工具
```
POST /api/soc/agent/tools/import-excel
Content-Type: multipart/form-data
Authorization: Bearer {token}

Request:
- file: Excel文件
- taskId: 123 (可选，不提供则需要用户选择)
- autoAnswer: true

Response:
{
    "code": 200,
    "message": "success", 
    "data": {
        "importJobId": "import_20240115_001",
        "taskId": 123,
        "message": "文件正在解析中，请稍候..."
    }
}
```

#### 2.7 数据分析工具
```
POST /api/soc/agent/tools/analyze-task
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "taskId": 123,
    "analysisType": "overview", // overview, product, assignee
    "filters": {
        "assignedTo": "张三（123456）" // 可选
    }
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "taskName": "中国电信5G基站建设项目SOC应答",
        "overview": {
            "totalCount": 50,
            "completedCount": 30,
            "completionRate": 60,
            "satisfactionRate": 85,
            "fcCount": 20,
            "pcCount": 8,
            "ncCount": 2
        },
        "productStats": [
            {
                "product": "华为防火墙",
                "totalCount": 15,
                "completedCount": 12,
                "satisfactionRate": 90
            }
        ],
        "summary": "当前任务总体进展良好，完成率60%，满足度85%。华为防火墙产品表现最佳。"
    }
}
```

#### 2.8 指派工具
```
POST /api/soc/agent/tools/assign-items
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "taskId": 123, // 可选
    "filters": {
        "products": ["华为防火墙"],
        "status": "未应答",
        "itemIds": [456, 457] // 可选，具体条目ID
    },
    "assignTo": "李四（789012）"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "assignedCount": 8,
        "message": "已将8个条目指派给李四（789012）"
    }
}
```

## 六、用户管理接口设计

### 1. 用户认证接口

#### 1.1 用户登录
```
POST /api/soc/auth/login
Content-Type: application/json

Request Body:
{
    "username": "zhangsan",
    "password": "password123",
    "captcha": "AB12", // 可选，验证码
    "captchaKey": "captcha_key_123" // 可选，验证码key
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "refresh_token_string",
        "expiresIn": 7200,
        "userInfo": {
            "userId": "123456",
            "username": "zhangsan", 
            "realName": "张三",
            "employeeId": "E123456",
            "email": "<EMAIL>",
            "department": "云服务产品部",
            "role": "SOC智能应答-普通用户",
            "permissions": [
                "/SOC标准库/网络安全",
                "/SOC标准库/云计算"
            ]
        }
    }
}
```

#### 1.2 刷新Token
```
POST /api/soc/auth/refresh
Content-Type: application/json

Request Body:
{
    "refreshToken": "refresh_token_string"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "token": "new_jwt_token",
        "expiresIn": 7200
    }
}
```

#### 1.3 用户登出
```
POST /api/soc/auth/logout
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success"
}
```

#### 1.4 获取当前用户信息
```
GET /api/soc/auth/me
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "userId": "123456",
        "username": "zhangsan",
        "realName": "张三", 
        "employeeId": "E123456",
        "email": "<EMAIL>",
        "department": "云服务产品部",
        "role": "SOC智能应答-普通用户",
        "permissions": [
            "/SOC标准库/网络安全",
            "/SOC标准库/云计算"
        ],
        "lastLoginTime": "2024-01-15 09:00:00"
    }
}
```

### 2. 权限管理接口

#### 2.1 检查用户权限
```
POST /api/soc/auth/check-permission
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "resource": "/SOC标准库/网络安全",
    "action": "read" // read, write
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "hasPermission": true
    }
}
```

#### 2.2 获取用户产品权限树
```
GET /api/soc/auth/product-permissions
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "permissions": [
            {
                "path": "/SOC标准库",
                "name": "SOC标准库",
                "children": [
                    {
                        "path": "/SOC标准库/网络安全",
                        "name": "网络安全",
                        "hasPermission": true,
                        "permissionType": "write"
                    },
                    {
                        "path": "/SOC标准库/云计算", 
                        "name": "云计算",
                        "hasPermission": true,
                        "permissionType": "read"
                    }
                ]
            }
        ]
    }
}
```

## 七、系统配置接口扩展

### 1. 数据源管理接口

#### 1.1 获取数据源配置
```
GET /api/soc/config/data-sources
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "sourceName": "GBBS",
            "sourceType": "GBBS",
            "isEnabled": true,
            "description": "GBBS系统数据源"
        },
        {
            "id": 2,
            "sourceName": "文档库",
            "sourceType": "DOC_LIB", 
            "isEnabled": false,
            "description": "内部文档库"
        }
    ]
}
```

### 2. 产品目录接口扩展

#### 2.1 获取产品目录树（支持用户权限过滤）
```
GET /api/soc/config/products-tree?withPermission=true&parentId=0
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "productCode": "SOC_STD",
            "productName": "SOC标准库",
            "productPath": "/SOC标准库",
            "hasPermission": true,
            "level": 1,
            "isLeaf": false,  
            "children": [
                {
                    "id": 11,
                    "productCode": "SOC_STD_NET",
                    "productName": "网络安全",
                    "productPath": "/SOC标准库/网络安全",
                    "hasPermission": true,
                    "level": 2,
                    "isLeaf": false,
                    "children": [
                        {
                            "id": 111,
                            "productCode": "HUAWEI_FW",
                            "productName": "华为防火墙",
                            "productPath": "/SOC标准库/网络安全/华为防火墙",
                            "hasPermission": true,
                            "level": 3,
                            "isLeaf": true
                        }
                    ]
                }
            ]
        }
    ]
}
```

### 3. 基础数据接口（GBBS集成）

#### 3.1 获取国家/MTO列表
```
GET /api/soc/config/countries?dataSource=GBBS
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "code": "CN",
            "name": "中国",
            "fullName": "中国/MTO"
        },
        {
            "code": "US", 
            "name": "美国",
            "fullName": "美国/MTO"
        }
    ]
}
```

#### 3.2 获取MTO分支列表
```
GET /api/soc/config/branches?country=CN&dataSource=GBBS
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "code": "BJ",
            "name": "北京分公司",
            "country": "中国"
        },
        {
            "code": "SH",
            "name": "上海分公司", 
            "country": "中国"
        }
    ]
}
```

#### 3.3 获取客户列表
```
GET /api/soc/config/customers?country=CN&branch=BJ&dataSource=GBBS&keyword=电信
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "code": "CHINATEL",
            "name": "中国电信",
            "fullName": "中国电信股份有限公司",
            "country": "中国",
            "branch": "北京分公司"
        }
    ]
}
```

#### 3.4 搜索项目
```
GET /api/soc/config/projects/search?keyword=5G&customer=中国电信&limit=10
Authorization: Bearer {token}

Response:
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "projectId": "PRJ001",
            "projectName": "5G基站建设项目",
            "customer": "中国电信",
            "description": "5G网络基础设施建设项目",
            "startDate": "2024-01-01"
        }
    ]
}
```

## 八、WebSocket实时通信接口

### 1. WebSocket连接
```
WS /api/soc/ws/connect?token={jwt_token}

连接成功后可以接收以下类型的消息：
```

### 2. 消息类型定义

#### 2.1 AI应答进度更新
```json
{
    "type": "ai_answer_progress",
    "data": {
        "jobId": "job_20240115_001",
        "taskId": 123,
        "progress": 75,
        "completedCount": 18,
        "totalCount": 25,
        "currentItem": "正在处理条目CODE018...",
        "estimatedEndTime": "2024-01-15 14:05:00"
    }
}
```

#### 2.2 导入进度更新
```json
{
    "type": "import_progress", 
    "data": {
        "importJobId": "import_20240115_001",
        "taskId": 123,
        "progress": 60,
        "processedRows": 30,
        "totalRows": 50,
        "currentRow": "正在处理第30行...",
        "errors": [
            {
                "row": 15,
                "error": "条目编号重复"
            }
        ]
    }
}
```

#### 2.3 任务状态变更通知
```json
{
    "type": "task_status_change",
    "data": {
        "taskId": 123,
        "taskName": "中国电信5G基站建设项目SOC应答",
        "oldStatus": "进行中",
        "newStatus": "已完成",
        "completionRate": 100,
        "message": "恭喜！任务已全部完成。"
    }
}
```

#### 2.4 条目指派通知
```json
{
    "type": "item_assigned",
    "data": {
        "taskId": 123,
        "taskName": "中国电信5G基站建设项目SOC应答", 
        "itemIds": [456, 457],
        "itemCount": 2,
        "assignedBy": "张三（123456）",
        "message": "您有2个新的条目需要应答"
    }
}
```

#### 2.5 系统通知
```json
{
    "type": "system_notification",
    "data": {
        "level": "info", // info, warning, error
        "title": "系统升级通知",
        "message": "系统将于今晚22:00-24:00进行升级维护，请及时保存工作内容。",
        "actionUrl": "/notifications/detail/123"
    }
}
```

## 九、错误码补充定义

### 业务错误码
- 2001: 任务名称已存在
- 2002: 条目编号在任务中已存在
- 2003: 产品权限不足
- 2004: 条目状态不允许此操作
- 2005: AI服务暂不可用
- 2006: 文件格式不支持
- 2007: 文件大小超出限制
- 2008: 导入数据格式错误
- 2009: 批量操作部分失败
- 2010: 匹配结果已过期
- 2011: 用户无权限访问该任务
- 2012: 条目正在应答中，请勿重复操作
- 2013: Agent服务不可用
- 2014: 对话上下文已过期

### Agent相关错误码  
- 3001: Agent理解失败
- 3002: 工具调用参数错误
- 3003: 工具执行超时
- 3004: 对话长度超出限制
- 3005: 不支持的工具类型
- 3006: Agent服务繁忙，请稍后再试

## 十、接口安全补充

### 1. 接口限流
```
基于用户的限流策略：
- 普通接口：100次/分钟
- AI应答接口：10次/分钟  
- 文件上传接口：5次/分钟
- Agent对话接口：20次/分钟
```

### 2. 数据权限控制
```
- 任务级权限：创建人可访问全部数据，指派人只能访问相关条目
- 产品级权限：用户只能操作已授权的产品范围
- 数据源权限：根据用户权限过滤可用数据源
- 接口权限：基于JWT Token验证用户身份和权限
```

### 3. 操作审计
```
关键操作自动记录审计日志：
- 任务创建、修改、删除
- 条目批量操作
- 权限变更操作
- 敏感数据导出操作
- Agent工具调用记录
```

本扩展设计为SOC智能应答系统提供了完整的Agent交互能力、用户管理功能和实时通信支持，确保系统的智能化、易用性和安全性。